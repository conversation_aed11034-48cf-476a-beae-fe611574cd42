2025-09-24 10:45:12,664 77432 INFO kayan_whatsapp odoo.modules.loading: 98 modules loaded in 3.47s, 570 queries (+570 extra) 
2025-09-24 10:45:13,425 77432 INFO kayan_whatsapp odoo.modules.registry: verifying fields for every extended model 
2025-09-24 10:45:13,560 77432 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18_cubes\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 10:45:17,971 113224 INFO ? odoo: Odoo version 18.0-******** 
2025-09-24 10:45:17,971 113224 INFO ? odoo: Using configuration file at C:\odoo18_cubes\server\odoo.conf 
2025-09-24 10:45:17,971 113224 INFO ? odoo: addons paths: ['C:\\odoo18_cubes\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18_cubes\\server\\custom_addons', 'c:\\odoo18_cubes\\server\\odoo\\addons'] 
2025-09-24 10:45:17,971 113224 INFO ? odoo: database: openpg@localhost:5432 
2025-09-24 10:45:18,198 113224 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-09-24 10:45:18,221 113224 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-09-24 10:45:19,865 113224 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8087 
2025-09-24 10:45:20,026 113224 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 10:45:20,026 113224 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-24 10:45:20,051 113224 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 10:45:20,062 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-09-24 10:45:20,326 113224 WARNING kayan_whatsapp odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 10:45:21,338 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['WhatsApp Evolution API Integration'] to user __system__ #1 via n/a 
2025-09-24 10:45:21,338 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['WhatsApp Evolution API Integration'] to user __system__ #1 via n/a 
2025-09-24 10:45:22,277 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-09-24 10:45:22,360 113224 INFO kayan_whatsapp odoo.modules.loading: loading 98 modules... 
2025-09-24 10:45:24,017 113224 WARNING kayan_whatsapp odoo.tools.translate: no translation language detected, skipping translation <frame at 0x000002867330CE10, file 'c:\\odoo18_cubes\\server\\custom_addons\\om_account_followup\\wizard\\followup_print.py', line 34, code FollowupPrint> 
Stack (most recent call last):
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18_cubes\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\__init__.py", line 1, in <module>
    from . import wizard
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\__init__.py", line 1, in <module>
    from . import followup_print
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 7, in <module>
    class FollowupPrint(models.TransientModel):
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 34, in FollowupPrint
    default=_('Invoices Reminder'))
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 609, in get_text_alias
    module, lang = _get_translation_source(1)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 598, in _get_translation_source
    lang = lang or _get_lang(frame, default_lang)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 589, in _get_lang
    _logger.log(log_level, 'no translation language detected, skipping translation %s', frame, stack_info=True)
2025-09-24 10:45:24,214 113224 INFO kayan_whatsapp odoo.modules.loading: Loading module whatsapp_evolution (79/98) 
2025-09-24 10:45:25,372 113224 INFO kayan_whatsapp odoo.modules.registry: module whatsapp_evolution: creating or updating database tables 
2025-09-24 10:45:26,030 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/security/whatsapp_evolution_security.xml 
2025-09-24 10:45:26,069 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/security/ir.model.access.csv 
2025-09-24 10:45:26,833 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_settings_data.xml 
2025-09-24 10:45:26,842 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_data.xml 
2025-09-24 10:45:26,861 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_default_data.xml 
2025-09-24 10:45:26,865 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_config_views.xml 
2025-09-24 10:45:26,974 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_settings_views.xml 
2025-09-24 10:45:27,015 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_normal_user_views.xml 
2025-09-24 10:45:27,073 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window.view records with IDs: [188, 189] 
2025-09-24 10:45:27,093 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_message_views.xml 
2025-09-24 10:45:27,138 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_send_message_wizard_views.xml 
2025-09-24 10:45:27,162 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_instance_wizard_views.xml 
2025-09-24 10:45:27,190 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,190 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,190 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,192 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-whatsapp text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 26,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,194 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: An alert (class alert-*) must have an alert, alertdialog or status role or an alert-link class. Please use alert and alertdialog only for what expects to stop any activity to be read immediately.
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 74,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,196 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-check-circle text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 116,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,197 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-exclamation-triangle text-danger) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 137,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,205 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_business_template_views.xml 
2025-09-24 10:45:27,268 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/wizard/whatsapp_sms_test_wizard_views.xml 
2025-09-24 10:45:27,285 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/menu_views.xml 
2025-09-24 10:45:27,440 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_business_template_data.xml 
2025-09-24 10:45:27,457 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_cron_data.xml 
2025-09-24 10:45:27,473 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module whatsapp_evolution: loading translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 10:45:27,473 113224 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 10:45:27,621 113224 INFO kayan_whatsapp odoo.modules.loading: Module whatsapp_evolution loaded in 3.41s, 554 queries (+554 other) 
2025-09-24 10:45:28,074 113224 INFO kayan_whatsapp odoo.modules.loading: 98 modules loaded in 5.71s, 554 queries (+554 extra) 
2025-09-24 10:45:30,036 113224 INFO kayan_whatsapp odoo.modules.registry: verifying fields for every extended model 
2025-09-24 10:45:30,448 113224 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18_cubes\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 10:45:32,546 113224 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 10:45:32,562 113224 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 10:45:32,571 113224 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 12.705s 
2025-09-24 10:45:32,579 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 10:45:35,653 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:35] "GET /odoo/action-691/3 HTTP/1.1" 200 - 99 0.160 15.538
2025-09-24 10:45:36,190 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 17 0.024 0.038
2025-09-24 10:45:36,230 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "POST /web/action/load HTTP/1.1" 200 - 10 0.031 0.066
2025-09-24 10:45:36,459 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.001 0.013
2025-09-24 10:45:36,541 113224 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-09-24 10:45:36,698 113224 WARNING kayan_whatsapp odoo.http: <function odoo.addons.bus.controllers.main.get_autovacuum_info> called ignoring args {'silent'} 
2025-09-24 10:45:36,708 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "POST /bus/get_autovacuum_info HTTP/1.1" 200 - 4 0.014 0.143
2025-09-24 10:45:36,784 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "POST /web/dataset/call_kw/whatsapp.business.template/get_views HTTP/1.1" 200 - 34 0.092 0.185
2025-09-24 10:45:36,805 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.015 0.017
2025-09-24 10:45:36,820 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "POST /mail/data HTTP/1.1" 200 - 69 0.294 0.080
2025-09-24 10:45:37,159 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:37] "POST /web/dataset/call_kw/whatsapp.business.template/web_read HTTP/1.1" 200 - 5 0.011 0.024
2025-09-24 10:45:38,791 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:38] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.006 0.002
2025-09-24 10:45:39,122 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "GET /odoo/action-691/3 HTTP/1.1" 200 - 15 0.042 0.040
2025-09-24 10:45:39,429 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.014 0.030
2025-09-24 10:45:39,729 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "POST /web/action/load HTTP/1.1" 200 - 9 0.016 0.019
2025-09-24 10:45:39,793 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "POST /web/dataset/call_kw/whatsapp.business.template/get_views HTTP/1.1" 200 - 2 0.000 0.038
2025-09-24 10:45:39,816 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "POST /mail/data HTTP/1.1" 200 - 38 0.053 0.057
2025-09-24 10:45:40,064 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:40] "POST /web/dataset/call_kw/whatsapp.business.template/web_read HTTP/1.1" 200 - 4 0.002 0.014
2025-09-24 10:45:40,136 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:40] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.008 0.006
2025-09-24 10:45:41,852 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:41] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.011
2025-09-24 10:45:42,118 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:42] "POST /web/dataset/call_kw/whatsapp.business.template/web_search_read HTTP/1.1" 200 - 3 0.016 0.002
2025-09-24 10:45:42,355 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:42] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.014
2025-09-24 10:45:45,609 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:45] "POST /web/action/load HTTP/1.1" 200 - 9 0.017 0.016
2025-09-24 10:45:45,964 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:45] "POST /web/dataset/call_kw/whatsapp.settings/get_views HTTP/1.1" 200 - 13 0.001 0.033
2025-09-24 10:45:46,198 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:46] "POST /web/dataset/call_kw/whatsapp.settings/web_search_read HTTP/1.1" 200 - 4 0.008 0.008
2025-09-24 10:45:47,496 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:47] "POST /web/dataset/call_kw/whatsapp.settings/web_search_read HTTP/1.1" 200 - 3 0.000 0.017
2025-09-24 10:45:48,993 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:48] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.010 0.016
2025-09-24 10:45:52,844 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:52] "POST /web/dataset/call_button/whatsapp.settings/action_test_sms HTTP/1.1" 200 - 2 0.009 0.008
2025-09-24 10:45:53,195 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:53] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/get_views HTTP/1.1" 200 - 10 0.002 0.025
2025-09-24 10:45:53,440 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:53] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/onchange HTTP/1.1" 200 - 2 0.000 0.016
2025-09-24 10:46:04,088 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:04] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/web_save HTTP/1.1" 200 - 5 0.008 0.017
2025-09-24 10:46:26,001 113224 WARNING kayan_whatsapp odoo.http: SMS Gateway connection error: HTTPConnectionPool(host='**************', port=8080): Max retries exceeded with url: /message (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000286737CE4B0>, 'Connection to ************** timed out. (connect timeout=30)')) 
2025-09-24 10:46:26,001 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:26] "POST /web/dataset/call_button/whatsapp.sms.test.wizard/action_send_test_sms HTTP/1.1" 200 - 3 0.000 21.596
2025-09-24 10:46:32,714 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 10:46:32,728 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.014s 
2025-09-24 10:46:32,738 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 10:46:32,745 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 10:46:32,762 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 10:46:32,769 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.006s 
2025-09-24 10:46:32,777 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 10:46:32,777 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 10:46:37,624 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:37] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.007 0.011
2025-09-24 10:46:42,316 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:42] "POST /web/dataset/call_kw/whatsapp.settings/web_save HTTP/1.1" 200 - 6 0.007 0.035
2025-09-24 10:46:43,526 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:43] "POST /web/dataset/call_button/whatsapp.settings/action_test_sms HTTP/1.1" 200 - 2 0.003 0.004
2025-09-24 10:46:43,853 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:43] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/onchange HTTP/1.1" 200 - 1 0.000 0.008
2025-09-24 10:46:52,484 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:52] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/web_save HTTP/1.1" 200 - 3 0.000 0.028
2025-09-24 10:47:13,894 113224 WARNING kayan_whatsapp odoo.http: SMS Gateway connection error: HTTPConnectionPool(host='**************', port=8080): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000002867378E6C0>, 'Connection to ************** timed out. (connect timeout=30)')) 
2025-09-24 10:47:13,894 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:47:13] "POST /web/dataset/call_button/whatsapp.sms.test.wizard/action_send_test_sms HTTP/1.1" 200 - 3 0.007 21.079
2025-09-24 10:49:55,687 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:49:55] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.001 0.017
2025-09-24 10:50:00,286 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:00] "POST /web/dataset/call_kw/whatsapp.settings/web_save HTTP/1.1" 200 - 5 0.006 0.026
2025-09-24 10:50:13,626 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:13] "POST /web/dataset/call_kw/whatsapp.settings/web_save HTTP/1.1" 200 - 5 0.000 0.025
2025-09-24 10:50:15,032 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:15] "POST /web/dataset/call_button/whatsapp.settings/action_test_sms HTTP/1.1" 200 - 2 0.000 0.007
2025-09-24 10:50:15,353 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:15] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/onchange HTTP/1.1" 200 - 1 0.008 0.000
2025-09-24 10:50:22,239 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:22] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/web_save HTTP/1.1" 200 - 3 0.002 0.007
2025-09-24 10:50:24,398 113224 WARNING kayan_whatsapp odoo.http: Unexpected error: SMS sending failed. Status: 202, Response: {"id":"-HGFO7wIzc-kP1ZcydViS","isEncrypted":false,"recipients":[{"phoneNumber":"+218915688883","state":"Pending"}],"state":"Pending","states":{"Pending":"2025-09-24T13:50:24.308+03:00"}} 
2025-09-24 10:50:24,398 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:24] "POST /web/dataset/call_button/whatsapp.sms.test.wizard/action_send_test_sms HTTP/1.1" 200 - 3 0.002 1.838
2025-09-24 10:51:32,830 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 10:51:32,836 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.006s 
2025-09-24 10:51:32,840 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 10:51:32,844 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 10:54:32,890 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 10:54:32,898 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.008s 
2025-09-24 10:54:32,906 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 10:54:32,906 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 10:56:32,954 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 10:56:32,963 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.009s 
2025-09-24 10:56:32,969 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 10:56:32,985 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 11:01:33,030 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 11:01:33,035 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.005s 
2025-09-24 11:01:33,041 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 11:01:33,047 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 11:04:33,084 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 11:04:33,091 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.007s 
2025-09-24 11:04:33,095 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 11:04:33,099 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 11:06:33,134 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 11:06:33,142 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.008s 
2025-09-24 11:06:33,147 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 11:06:33,155 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 11:11:33,236 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 11:11:33,408 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.172s 
2025-09-24 11:11:33,416 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 11:11:33,426 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:07:15,757 113224 INFO kayan_whatsapp odoo.addons.base.models.res_device: User 2 inserts device log (OvaDjCeDiQ01hvjXuCefi2FhSRa9Md5SDz5j6MW0XY) 
2025-09-24 12:07:15,763 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:07:15] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 2 0.026 0.078
2025-09-24 12:07:19,871 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-09-24 12:07:19,978 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-09-24 12:07:20,015 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.cron.progress records with IDs: [1, 2] 
2025-09-24 12:07:20,349 113224 INFO kayan_whatsapp odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-09-24 12:07:20,380 113224 INFO kayan_whatsapp odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-09-24 12:07:20,399 113224 INFO kayan_whatsapp odoo.addons.base.models.res_device: GC device logs delete 3 entries 
2025-09-24 12:07:20,403 113224 INFO kayan_whatsapp odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-09-24 12:07:20,472 113224 INFO kayan_whatsapp odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-09-24 12:07:20,513 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted bus.bus records with IDs: [163, 164, 165, 166, 167, 168, 169, 170, 171] 
2025-09-24 12:07:20,955 113224 INFO kayan_whatsapp odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-09-24 12:07:22,040 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted whatsapp.template.test.wizard records with IDs: [3] 
2025-09-24 12:07:22,055 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted whatsapp.sms.test.wizard records with IDs: [1, 2, 3] 
2025-09-24 12:07:22,132 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 2.261s 
2025-09-24 12:07:22,141 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,152 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-09-24 12:07:22,174 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-09-24 12:07:22,186 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.012s 
2025-09-24 12:07:22,197 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,204 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-09-24 12:07:22,219 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-09-24 12:07:22,231 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.012s 
2025-09-24 12:07:22,235 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,246 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-09-24 12:07:22,262 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-09-24 12:07:22,280 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.017s 
2025-09-24 12:07:22,285 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,291 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-09-24 12:07:22,308 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 12:07:22,321 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.013s 
2025-09-24 12:07:22,330 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,335 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 12:07:22,349 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (49) starting 
2025-09-24 12:07:22,356 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (49) done in 0.007s 
2025-09-24 12:07:22,363 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (49) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,373 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (49) completed 
2025-09-24 12:07:22,388 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) starting 
2025-09-24 12:07:22,477 113224 INFO kayan_whatsapp odoo.addons.whatsapp_evolution.models.whatsapp_stock_monitor: No low stock products found 
2025-09-24 12:07:22,478 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) done in 0.090s 
2025-09-24 12:07:22,485 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,491 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) completed 
2025-09-24 12:07:22,509 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:07:22,519 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.010s 
2025-09-24 12:07:22,523 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,534 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:07:22,548 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-09-24 12:07:22,574 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.025s 
2025-09-24 12:07:22,581 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,587 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-09-24 12:07:22,606 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-09-24 12:07:22,620 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.013s 
2025-09-24 12:07:22,625 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,636 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-09-24 12:07:22,658 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) starting 
2025-09-24 12:07:22,676 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) done in 0.018s 
2025-09-24 12:07:22,680 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,688 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) completed 
2025-09-24 12:10:28,796 113224 WARNING kayan_whatsapp odoo.http: <function odoo.addons.bus.controllers.main.get_autovacuum_info> called ignoring args {'silent'} 
2025-09-24 12:10:28,796 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:28] "POST /bus/get_autovacuum_info HTTP/1.1" 200 - 3 0.001 0.025
2025-09-24 12:10:35,875 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:35] "GET /odoo/action-696/1 HTTP/1.1" 200 - 15 0.003 0.038
2025-09-24 12:10:36,236 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:36] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.007 0.020
2025-09-24 12:10:36,545 113224 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:36] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 304 - 0 0.000 0.022
2025-09-24 12:10:36,599 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:36] "POST /web/action/load HTTP/1.1" 200 - 9 0.059 0.014
2025-09-24 12:10:36,756 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:36] "POST /mail/data HTTP/1.1" 200 - 38 0.184 0.047
2025-09-24 12:10:36,904 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:36] "POST /web/dataset/call_kw/whatsapp.settings/get_views HTTP/1.1" 200 - 1 0.001 0.012
2025-09-24 12:10:36,907 113224 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:36] "GET /web/static/img/favicon.ico HTTP/1.1" 304 - 0 0.000 0.018
2025-09-24 12:10:36,924 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:36] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.001 0.004
2025-09-24 12:10:37,232 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:37] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.009 0.003
2025-09-24 12:10:38,809 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:38] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.002
2025-09-24 12:10:39,581 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:39] "POST /web/dataset/call_button/whatsapp.settings/action_test_sms HTTP/1.1" 200 - 2 0.000 0.003
2025-09-24 12:10:39,893 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:39] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/get_views HTTP/1.1" 200 - 1 0.002 0.000
2025-09-24 12:10:40,167 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:10:40] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/onchange HTTP/1.1" 200 - 1 0.000 0.003
2025-09-24 12:11:09,923 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:11:09] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/web_save HTTP/1.1" 200 - 3 0.006 0.002
2025-09-24 12:11:26,792 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:11:26,808 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.016s 
2025-09-24 12:11:26,808 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:11:26,808 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:11:31,315 113224 WARNING kayan_whatsapp odoo.http: SMS Gateway connection error: HTTPConnectionPool(host='***********', port=8080): Max retries exceeded with url: /message (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000286735EAB40>, 'Connection to *********** timed out. (connect timeout=30)')) 
2025-09-24 12:11:31,315 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:11:31] "POST /web/dataset/call_button/whatsapp.sms.test.wizard/action_send_test_sms HTTP/1.1" 200 - 3 0.005 21.071
2025-09-24 12:12:04,800 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:04] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.000 0.011
2025-09-24 12:12:17,862 66872 INFO ? odoo: Odoo version 18.0-******** 
2025-09-24 12:12:17,862 66872 INFO ? odoo: Using configuration file at C:\odoo18_cubes\server\odoo.conf 
2025-09-24 12:12:17,862 66872 INFO ? odoo: addons paths: ['C:\\odoo18_cubes\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18_cubes\\server\\custom_addons', 'c:\\odoo18_cubes\\server\\odoo\\addons'] 
2025-09-24 12:12:17,862 66872 INFO ? odoo: database: openpg@localhost:5432 
2025-09-24 12:12:18,267 66872 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-09-24 12:12:18,288 66872 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-09-24 12:12:19,939 66872 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8087 
2025-09-24 12:12:20,027 66872 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:12:20,072 66872 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-09-24 12:12:20,105 66872 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:12:20,105 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-09-24 12:12:20,890 66872 WARNING kayan_whatsapp odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 12:12:28,507 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['WhatsApp Evolution API Integration'] to user __system__ #1 via n/a 
2025-09-24 12:12:28,507 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['WhatsApp Evolution API Integration'] to user __system__ #1 via n/a 
2025-09-24 12:12:29,687 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-09-24 12:12:29,791 66872 INFO kayan_whatsapp odoo.modules.loading: loading 98 modules... 
2025-09-24 12:12:31,806 66872 WARNING kayan_whatsapp odoo.tools.translate: no translation language detected, skipping translation <frame at 0x000002264F4C92F0, file 'c:\\odoo18_cubes\\server\\custom_addons\\om_account_followup\\wizard\\followup_print.py', line 34, code FollowupPrint> 
Stack (most recent call last):
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18_cubes\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\__init__.py", line 1, in <module>
    from . import wizard
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\__init__.py", line 1, in <module>
    from . import followup_print
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 7, in <module>
    class FollowupPrint(models.TransientModel):
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 34, in FollowupPrint
    default=_('Invoices Reminder'))
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 609, in get_text_alias
    module, lang = _get_translation_source(1)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 598, in _get_translation_source
    lang = lang or _get_lang(frame, default_lang)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 589, in _get_lang
    _logger.log(log_level, 'no translation language detected, skipping translation %s', frame, stack_info=True)
2025-09-24 12:12:31,890 66872 INFO kayan_whatsapp odoo.modules.loading: Loading module whatsapp_evolution (79/98) 
2025-09-24 12:12:32,207 66872 INFO kayan_whatsapp odoo.modules.registry: module whatsapp_evolution: creating or updating database tables 
2025-09-24 12:12:32,454 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/security/whatsapp_evolution_security.xml 
2025-09-24 12:12:32,499 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/security/ir.model.access.csv 
2025-09-24 12:12:32,806 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_settings_data.xml 
2025-09-24 12:12:32,824 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_data.xml 
2025-09-24 12:12:32,844 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_default_data.xml 
2025-09-24 12:12:32,861 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_config_views.xml 
2025-09-24 12:12:32,990 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_settings_views.xml 
2025-09-24 12:12:33,036 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_normal_user_views.xml 
2025-09-24 12:12:33,099 66872 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window.view records with IDs: [190, 191] 
2025-09-24 12:12:33,111 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_message_views.xml 
2025-09-24 12:12:33,150 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_send_message_wizard_views.xml 
2025-09-24 12:12:33,183 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_instance_wizard_views.xml 
2025-09-24 12:12:33,207 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,207 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,207 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,207 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,207 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,207 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,207 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,207 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,211 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-whatsapp text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 26,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,211 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: An alert (class alert-*) must have an alert, alertdialog or status role or an alert-link class. Please use alert and alertdialog only for what expects to stop any activity to be read immediately.
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 74,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,211 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-check-circle text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 116,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,211 66872 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-exclamation-triangle text-danger) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 137,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:12:33,215 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_business_template_views.xml 
2025-09-24 12:12:33,273 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/wizard/whatsapp_sms_test_wizard_views.xml 
2025-09-24 12:12:33,300 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/menu_views.xml 
2025-09-24 12:12:33,409 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_business_template_data.xml 
2025-09-24 12:12:33,424 66872 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_cron_data.xml 
2025-09-24 12:12:33,450 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module whatsapp_evolution: loading translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 12:12:33,457 66872 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 12:12:33,634 66872 INFO kayan_whatsapp odoo.modules.loading: Module whatsapp_evolution loaded in 1.74s, 552 queries (+552 other) 
2025-09-24 12:12:33,889 66872 INFO kayan_whatsapp odoo.modules.loading: 98 modules loaded in 4.10s, 552 queries (+552 extra) 
2025-09-24 12:12:34,432 66872 INFO kayan_whatsapp odoo.modules.registry: verifying fields for every extended model 
2025-09-24 12:12:34,693 66872 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18_cubes\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:12:35,618 66872 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:12:35,624 66872 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:12:35,625 66872 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 15.684s 
2025-09-24 12:12:35,627 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:12:35,841 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:35] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 7 0.005 9.995
2025-09-24 12:12:36,190 66872 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-09-24 12:12:36,723 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:36] "GET /odoo/action-696/1 HTTP/1.1" 200 - 94 0.087 16.665
2025-09-24 12:12:36,926 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:36] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 13 0.016 0.012
2025-09-24 12:12:36,931 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:36] "POST /web/action/load HTTP/1.1" 200 - 10 0.007 0.024
2025-09-24 12:12:37,289 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:37] "POST /web/dataset/call_kw/whatsapp.settings/get_views HTTP/1.1" 200 - 16 0.014 0.036
2025-09-24 12:12:37,306 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:37] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.016 0.034
2025-09-24 12:12:37,356 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:37] "POST /mail/data HTTP/1.1" 200 - 69 0.076 0.072
2025-09-24 12:12:37,610 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:37] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 4 0.004 0.000
2025-09-24 12:12:39,234 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:12:39] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.000
2025-09-24 12:13:32,809 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:13:32] "POST /web/dataset/call_kw/whatsapp.settings/web_save HTTP/1.1" 200 - 6 0.014 0.003
2025-09-24 12:13:34,423 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:13:34] "POST /web/dataset/call_button/whatsapp.settings/action_test_sms HTTP/1.1" 200 - 2 0.000 0.000
2025-09-24 12:13:34,682 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:13:34] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/get_views HTTP/1.1" 200 - 10 0.000 0.000
2025-09-24 12:13:34,762 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:13:34] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/onchange HTTP/1.1" 200 - 2 0.007 0.000
2025-09-24 12:13:55,348 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:13:55] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/web_save HTTP/1.1" 200 - 5 0.007 0.001
2025-09-24 12:13:56,295 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:13:56] "POST /web/dataset/call_button/whatsapp.sms.test.wizard/action_send_test_sms HTTP/1.1" 200 - 3 0.000 0.627
2025-09-24 12:14:35,839 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 12:14:35,848 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.008s 
2025-09-24 12:14:35,848 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 12:14:35,856 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 12:14:39,006 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:14:39] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.003 0.007
2025-09-24 12:16:35,884 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:16:35,884 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.000s 
2025-09-24 12:16:35,894 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:16:35,894 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:18:35,923 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) starting 
2025-09-24 12:18:36,071 66872 INFO kayan_whatsapp odoo.addons.whatsapp_evolution.models.whatsapp_stock_monitor: No low stock products found 
2025-09-24 12:18:36,071 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) done in 0.143s 
2025-09-24 12:18:36,071 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) processed 0 records, 0 records remaining 
2025-09-24 12:18:36,071 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) completed 
2025-09-24 12:21:36,108 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:21:36,115 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.007s 
2025-09-24 12:21:36,119 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:21:36,125 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:23:36,169 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-09-24 12:23:36,183 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.011s 
2025-09-24 12:23:36,183 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-09-24 12:23:36,183 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-09-24 12:23:36,197 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-09-24 12:23:36,217 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.020s 
2025-09-24 12:23:36,217 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-09-24 12:23:36,217 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-09-24 12:23:47,048 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-09-24 12:23:47,064 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.016s 
2025-09-24 12:23:47,083 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-09-24 12:23:47,089 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-09-24 12:23:47,096 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-09-24 12:23:47,111 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.016s 
2025-09-24 12:23:47,111 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-09-24 12:23:47,111 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-09-24 12:24:36,263 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-09-24 12:24:36,276 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.012s 
2025-09-24 12:24:36,282 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-09-24 12:24:36,287 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-09-24 12:24:36,302 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 12:24:36,308 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.006s 
2025-09-24 12:24:36,314 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 12:24:36,319 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 12:26:36,350 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:26:36,353 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.003s 
2025-09-24 12:26:36,355 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:26:36,359 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:28:31,674 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:31] "POST /web/action/load HTTP/1.1" 200 - 10 0.027 0.055
2025-09-24 12:28:32,352 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:32] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 54 0.132 0.194
2025-09-24 12:28:32,383 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:32] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.006
2025-09-24 12:28:32,749 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:32] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.046 0.017
2025-09-24 12:28:32,826 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:32] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.008 0.126
2025-09-24 12:28:52,030 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "POST /web/dataset/call_button/ir.module.module/more_info HTTP/1.1" 200 - 1 0.004 0.012
2025-09-24 12:28:52,290 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 7 0.008 0.013
2025-09-24 12:28:52,424 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "POST /web/dataset/call_kw/ir.module.module/web_read HTTP/1.1" 200 - 16 0.012 0.055
2025-09-24 12:28:52,685 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "GET /base/static/src/css/description.css HTTP/1.1" 200 - 0 0.000 0.072
2025-09-24 12:28:52,799 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "GET /nati_arabic_font/static/src/fonts/Cairo/Cairo-Light.woff HTTP/1.1" 200 - 0 0.000 0.021
2025-09-24 12:28:52,803 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "GET /web/static/fonts/twitter_x_only.woff HTTP/1.1" 200 - 0 0.000 0.027
2025-09-24 12:28:52,832 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "GET /web/image/ir.module.module/352/icon_image?unique=************* HTTP/1.1" 200 - 3 0.005 0.033
2025-09-24 12:28:52,834 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "GET /om_account_accountant/static/description/odoo_github.png HTTP/1.1" 200 - 0 0.000 0.033
2025-09-24 12:28:52,843 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:52] "GET /om_account_accountant/static/description/odoowalnut.png HTTP/1.1" 200 - 0 0.000 0.047
2025-09-24 12:28:53,027 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/financial_reports.png HTTP/1.1" 200 - 0 0.000 0.021
2025-09-24 12:28:53,145 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/odoo18_asset_management.png HTTP/1.1" 200 - 0 0.000 0.023
2025-09-24 12:28:53,151 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/odoo18_budget.png HTTP/1.1" 200 - 0 0.000 0.030
2025-09-24 12:28:53,175 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/account_dashboard.png HTTP/1.1" 200 - 0 0.000 0.025
2025-09-24 12:28:53,175 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/customer_followup.png HTTP/1.1" 200 - 0 0.000 0.024
2025-09-24 12:28:53,192 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/recurring_payment.png HTTP/1.1" 200 - 0 0.000 0.037
2025-09-24 12:28:53,368 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/daily_reports.png HTTP/1.1" 200 - 0 0.000 0.015
2025-09-24 12:28:53,480 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/odoo18_fiscal_year.png HTTP/1.1" 200 - 0 0.000 0.024
2025-09-24 12:28:53,494 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/account_settings.png HTTP/1.1" 200 - 0 0.000 0.022
2025-09-24 12:28:53,498 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/odoo_report.gif HTTP/1.1" 200 - 0 0.000 0.008
2025-09-24 12:28:53,519 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/account_settings2.png HTTP/1.1" 200 - 0 0.000 0.034
2025-09-24 12:28:53,525 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:28:53] "GET /om_account_accountant/static/description/odoo_mates.png HTTP/1.1" 200 - 0 0.000 0.025
2025-09-24 12:30:09,041 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:09] "POST /web/action/load HTTP/1.1" 200 - 10 0.017 0.031
2025-09-24 12:30:09,403 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:09] "POST /web/dataset/call_kw/account.journal/get_views HTTP/1.1" 200 - 50 0.043 0.107
2025-09-24 12:30:09,726 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:09] "POST /web/dataset/call_kw/account.journal/web_search_read HTTP/1.1" 200 - 38 0.180 0.132
2025-09-24 12:30:10,063 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:10] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.004 0.000
2025-09-24 12:30:10,092 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:10] "GET /web/bundle/web.chartjs_lib?lang=en_US&debug=1 HTTP/1.1" 200 - 2 0.005 0.020
2025-09-24 12:30:11,808 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:11] "POST /web/action/load HTTP/1.1" 200 - 9 0.019 0.023
2025-09-24 12:30:12,258 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:12] "POST /web/dataset/call_kw/stock.picking.type/get_views HTTP/1.1" 200 - 48 0.067 0.048
2025-09-24 12:30:12,491 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:12] "POST /web/dataset/call_kw/stock.picking.type/web_search_read HTTP/1.1" 200 - 22 0.068 0.031
2025-09-24 12:30:15,994 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:15] "POST /web/action/load HTTP/1.1" 200 - 11 0.007 0.030
2025-09-24 12:30:16,425 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:16] "POST /web/dataset/call_kw/product.category/get_views HTTP/1.1" 200 - 28 0.033 0.068
2025-09-24 12:30:16,597 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:16] "POST /web/dataset/call_kw/product.category/web_search_read HTTP/1.1" 200 - 3 0.022 0.000
2025-09-24 12:30:16,755 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:16] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.008
2025-09-24 12:30:20,348 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:20] "POST /web/dataset/call_kw/product.category/web_read HTTP/1.1" 200 - 18 0.054 0.038
2025-09-24 12:30:20,761 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:20] "POST /mail/thread/data HTTP/1.1" 200 - 16 0.041 0.026
2025-09-24 12:30:20,861 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:20] "POST /mail/thread/messages HTTP/1.1" 200 - 22 0.122 0.031
2025-09-24 12:30:24,994 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:24] "POST /web/dataset/call_kw/product.removal/name_search HTTP/1.1" 200 - 3 0.012 0.002
2025-09-24 12:30:30,510 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:30] "POST /web/dataset/call_kw/product.category/onchange HTTP/1.1" 200 - 6 0.005 0.027
2025-09-24 12:30:34,381 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:34] "POST /web/dataset/call_kw/product.category/onchange HTTP/1.1" 200 - 6 0.003 0.018
2025-09-24 12:30:38,148 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:38] "POST /web/dataset/call_kw/product.category/onchange HTTP/1.1" 200 - 6 0.003 0.019
2025-09-24 12:30:41,906 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:41] "POST /web/dataset/call_kw/product.category/web_save HTTP/1.1" 200 - 13 0.028 0.019
2025-09-24 12:30:42,267 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:42] "GET /odoo/action-183/10?debug=1 HTTP/1.1" 200 - 15 0.022 0.062
2025-09-24 12:30:42,859 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:42] "POST /web/action/load HTTP/1.1" 200 - 11 0.020 0.009
2025-09-24 12:30:42,879 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:42] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.014 0.033
2025-09-24 12:30:43,116 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:43] "POST /web/dataset/call_kw/product.category/get_views HTTP/1.1" 200 - 2 0.001 0.024
2025-09-24 12:30:43,158 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:43] "POST /mail/data HTTP/1.1" 200 - 38 0.272 0.055
2025-09-24 12:30:43,204 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:43] "POST /web/dataset/call_kw/product.category/web_read HTTP/1.1" 200 - 12 0.006 0.019
2025-09-24 12:30:43,216 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:43] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.009 0.012
2025-09-24 12:30:43,476 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:43] "POST /mail/thread/data HTTP/1.1" 200 - 11 0.012 0.021
2025-09-24 12:30:43,577 66872 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:43] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 12:30:43,627 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:43] "POST /mail/thread/messages HTTP/1.1" 200 - 20 0.056 0.010
2025-09-24 12:30:45,101 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:45] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.006
2025-09-24 12:30:45,986 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:45] "POST /web/dataset/call_kw/product.removal/name_search HTTP/1.1" 200 - 2 0.000 0.009
2025-09-24 12:30:49,451 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:49] "POST /web/dataset/call_kw/product.removal/name_search HTTP/1.1" 200 - 2 0.003 0.002
2025-09-24 12:30:52,090 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:52] "POST /web/dataset/call_kw/product.removal/name_search HTTP/1.1" 200 - 2 0.010 0.002
2025-09-24 12:30:54,050 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:54] "POST /web/dataset/call_kw/product.removal/name_search HTTP/1.1" 200 - 2 0.002 0.009
2025-09-24 12:30:57,807 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:57] "POST /web/dataset/call_kw/account.account/name_search HTTP/1.1" 200 - 5 0.021 0.023
2025-09-24 12:30:58,403 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:58] "GET /nati_arabic_font/static/src/fonts/Cairo/Cairo-RegularItalic.woff HTTP/1.1" 404 - 68 0.064 0.314
2025-09-24 12:30:58,452 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:30:58] "GET /nati_arabic_font/static/src/fonts/Cairo/Cairo-RegularItalic.ttf HTTP/1.1" 404 - 12 0.017 0.022
2025-09-24 12:31:36,452 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:31:36,462 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.010s 
2025-09-24 12:31:36,465 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:31:36,467 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:31:51,619 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:31:51] "POST /web/action/load HTTP/1.1" 200 - 10 0.012 0.012
2025-09-24 12:31:51,864 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:31:51] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 2 0.003 0.014
2025-09-24 12:31:51,949 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:31:51] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.009
2025-09-24 12:31:52,260 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:31:52] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.019 0.061
2025-09-24 12:31:52,272 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:31:52] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.051 0.043
2025-09-24 12:31:56,306 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:31:56] "POST /web/dataset/call_button/ir.module.module/more_info HTTP/1.1" 200 - 1 0.002 0.006
2025-09-24 12:31:56,642 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:31:56] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 2 0.000 0.016
2025-09-24 12:31:56,918 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:31:56] "POST /web/dataset/call_kw/ir.module.module/web_read HTTP/1.1" 200 - 14 0.014 0.024
2025-09-24 12:33:57,357 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 12:33:57,365 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.008s 
2025-09-24 12:33:57,374 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 12:33:57,386 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 12:36:36,549 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:36:36,549 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.000s 
2025-09-24 12:36:36,549 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:36:36,565 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:37:28,069 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:28] "POST /web/action/load HTTP/1.1" 200 - 10 0.016 0.010
2025-09-24 12:37:28,325 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:28] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 2 0.000 0.020
2025-09-24 12:37:28,392 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:28] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.000
2025-09-24 12:37:28,770 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:28] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.087 0.020
2025-09-24 12:37:28,782 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:28] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.001 0.116
2025-09-24 12:37:32,828 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:32] "POST /web/action/load HTTP/1.1" 200 - 9 0.006 0.029
2025-09-24 12:37:34,238 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:34] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 392 0.254 0.821
2025-09-24 12:37:34,543 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:34] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 205 0.188 0.104
2025-09-24 12:37:34,960 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:34] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.000 0.013
2025-09-24 12:37:34,984 66872 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:37:34] "POST /base_setup/data HTTP/1.1" 200 - 6 0.024 0.000
2025-09-24 12:41:36,630 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:41:36,639 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.009s 
2025-09-24 12:41:36,650 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:41:36,657 66872 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:42:41,449 127508 INFO ? odoo: Odoo version 18.0-******** 
2025-09-24 12:42:41,449 127508 INFO ? odoo: Using configuration file at C:\odoo18_cubes\server\odoo.conf 
2025-09-24 12:42:41,449 127508 INFO ? odoo: addons paths: ['C:\\odoo18_cubes\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18_cubes\\server\\custom_addons', 'c:\\odoo18_cubes\\server\\odoo\\addons'] 
2025-09-24 12:42:41,449 127508 INFO ? odoo: database: openpg@localhost:5432 
2025-09-24 12:42:41,873 127508 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-09-24 12:42:41,898 127508 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-09-24 12:42:43,493 127508 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8087 
2025-09-24 12:42:43,591 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:42:43,643 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.05s, 0 queries (+0 extra) 
2025-09-24 12:42:43,664 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:42:43,664 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-09-24 12:42:44,103 127508 WARNING kayan_whatsapp odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 12:42:45,469 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['WhatsApp Evolution API Integration'] to user __system__ #1 via n/a 
2025-09-24 12:42:45,470 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['WhatsApp Evolution API Integration'] to user __system__ #1 via n/a 
2025-09-24 12:42:46,179 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-09-24 12:42:46,256 127508 INFO kayan_whatsapp odoo.modules.loading: loading 98 modules... 
2025-09-24 12:42:47,570 127508 WARNING kayan_whatsapp odoo.tools.translate: no translation language detected, skipping translation <frame at 0x00000261067CD7D0, file 'c:\\odoo18_cubes\\server\\custom_addons\\om_account_followup\\wizard\\followup_print.py', line 34, code FollowupPrint> 
Stack (most recent call last):
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18_cubes\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\__init__.py", line 1, in <module>
    from . import wizard
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\__init__.py", line 1, in <module>
    from . import followup_print
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 7, in <module>
    class FollowupPrint(models.TransientModel):
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 34, in FollowupPrint
    default=_('Invoices Reminder'))
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 609, in get_text_alias
    module, lang = _get_translation_source(1)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 598, in _get_translation_source
    lang = lang or _get_lang(frame, default_lang)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 589, in _get_lang
    _logger.log(log_level, 'no translation language detected, skipping translation %s', frame, stack_info=True)
2025-09-24 12:42:47,665 127508 INFO kayan_whatsapp odoo.modules.loading: Loading module whatsapp_evolution (79/98) 
2025-09-24 12:42:48,363 127508 INFO kayan_whatsapp odoo.modules.registry: module whatsapp_evolution: creating or updating database tables 
2025-09-24 12:42:48,644 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/security/whatsapp_evolution_security.xml 
2025-09-24 12:42:48,686 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/security/ir.model.access.csv 
2025-09-24 12:42:49,384 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_settings_data.xml 
2025-09-24 12:42:49,395 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_data.xml 
2025-09-24 12:42:49,406 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_default_data.xml 
2025-09-24 12:42:49,413 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_config_views.xml 
2025-09-24 12:42:49,********** INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_settings_views.xml 
2025-09-24 12:42:49,572 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_normal_user_views.xml 
2025-09-24 12:42:49,630 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window.view records with IDs: [192, 193] 
2025-09-24 12:42:49,643 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_message_views.xml 
2025-09-24 12:42:49,693 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_send_message_wizard_views.xml 
2025-09-24 12:42:49,715 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_instance_wizard_views.xml 
2025-09-24 12:42:49,732 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,732 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,737 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,737 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,737 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,737 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,737 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,737 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,739 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-whatsapp text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 26,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,741 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: An alert (class alert-*) must have an alert, alertdialog or status role or an alert-link class. Please use alert and alertdialog only for what expects to stop any activity to be read immediately.
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 74,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,741 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-check-circle text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 116,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,743 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-exclamation-triangle text-danger) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 137,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 12:42:49,752 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_business_template_views.xml 
2025-09-24 12:42:49,845 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/wizard/whatsapp_sms_test_wizard_views.xml 
2025-09-24 12:42:49,881 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/menu_views.xml 
2025-09-24 12:42:50,011 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_business_template_data.xml 
2025-09-24 12:42:50,026 127508 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_cron_data.xml 
2025-09-24 12:42:50,038 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module whatsapp_evolution: loading translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 12:42:50,039 127508 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 12:42:50,154 127508 INFO kayan_whatsapp odoo.modules.loading: Module whatsapp_evolution loaded in 2.49s, 552 queries (+552 other) 
2025-09-24 12:42:50,562 127508 INFO kayan_whatsapp odoo.modules.loading: 98 modules loaded in 4.31s, 552 queries (+552 extra) 
2025-09-24 12:42:51,361 127508 INFO kayan_whatsapp odoo.modules.registry: verifying fields for every extended model 
2025-09-24 12:42:51,579 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18_cubes\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:42:53,143 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:42:53,161 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:42:53,164 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 9.667s 
2025-09-24 12:42:53,164 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:42:53,476 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:53] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 12 0.013 8.380
2025-09-24 12:42:53,512 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:53] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 3 0.040 6.343
2025-09-24 12:42:53,609 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:53] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.137 8.387
2025-09-24 12:42:53,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:53] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 7 0.072 8.543
2025-09-24 12:42:53,960 127508 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-09-24 12:42:54,147 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:54] "GET /base_accounting_kit/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-09-24 12:42:55,795 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:55] "GET /odoo/settings?debug=1 HTTP/1.1" 200 - 94 0.269 6.843
2025-09-24 12:42:56,395 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:56] "POST /web/action/load HTTP/1.1" 200 - 10 0.022 0.020
2025-09-24 12:42:56,582 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:56] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.099 0.023
2025-09-24 12:42:56,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:56] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.023 0.051
2025-09-24 12:42:56,885 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:56] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.000 0.040
2025-09-24 12:42:56,930 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:56] "POST /mail/data HTTP/1.1" 200 - 69 0.429 0.057
2025-09-24 12:42:57,206 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:57] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.000
2025-09-24 12:42:58,001 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:58] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 396 0.262 1.294
2025-09-24 12:42:58,213 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:58] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 209 0.111 0.086
2025-09-24 12:42:58,573 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:42:58] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.004
2025-09-24 12:43:01,741 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:01] "POST /web/action/load HTTP/1.1" 200 - 10 0.000 0.032
2025-09-24 12:43:02,119 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:02] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 49 0.077 0.069
2025-09-24 12:43:02,137 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:02] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.002 0.004
2025-09-24 12:43:02,554 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:02] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.058 0.052
2025-09-24 12:43:02,561 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:02] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.007 0.110
2025-09-24 12:43:04,278 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:04] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.048 0.035
2025-09-24 12:43:04,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:04] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.014 0.156
2025-09-24 12:43:07,939 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['Odoo 18 Accounting Community'] to user admin #2 via 127.0.0.1 
2025-09-24 12:43:07,941 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:07] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 3 0.004 0.010
2025-09-24 12:43:08,328 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:08] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 15 0.017 0.029
2025-09-24 12:43:08,572 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:08] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 9 0.029 0.011
2025-09-24 12:43:10,327 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:10] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 11 0.019 0.037
2025-09-24 12:43:10,662 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['Odoo 18 Accounting Community'] to user admin #2 via 127.0.0.1 
2025-09-24 12:43:10,662 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-09-24 12:43:10,662 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['Odoo 18 Accounting Community'] to user admin #2 via 127.0.0.1 
2025-09-24 12:43:10,761 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:43:10,778 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-09-24 12:43:10,827 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:43:10,827 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:43:12,140 127508 INFO kayan_whatsapp odoo.modules.loading: loading 98 modules... 
2025-09-24 12:43:12,240 127508 INFO kayan_whatsapp odoo.modules.loading: 98 modules loaded in 0.10s, 0 queries (+0 extra) 
2025-09-24 12:43:12,904 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['Odoo 18 Accounting Community'] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:43:12,918 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1943,) 
2025-09-24 12:43:13,324 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\wizard\base_module_uninstall.py", line 63, in action_uninstall
    return modules.button_immediate_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 633, in button_immediate_uninstall
    return self._button_immediate_function(self.env.registry[self._name].button_uninstall)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 558, in load_modules
    Module.browse(modules_to_remove.values()).module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\account\models\ir_module.py", line 109, in module_uninstall
    return super().module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\base_import_module\models\ir_module.py", line 327, in module_uninstall
    res = super().module_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 494, in module_uninstall
    self.env['ir.model.data']._module_data_uninstall(modules_to_remove)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2522, in _module_data_uninstall
    delete(self.env[model].browse(item[1] for item in items))
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2510, in delete
    records.unlink()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 557, in unlink
    return super(View, self).unlink()
  File "C:\odoo18_cubes\server\odoo\models.py", line 4491, in unlink
    self.modified(self._fields, before=True)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:43:13,376 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57488] 
2025-09-24 12:43:13,376 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1943] 
2025-09-24 12:43:13,387 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.server(681,) 
2025-09-24 12:43:13,453 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57487] 
2025-09-24 12:43:13,453 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.server records with IDs: [681] 
2025-09-24 12:43:13,453 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(532,) 
2025-09-24 12:43:13,477 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57486] 
2025-09-24 12:43:13,477 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [532] 
2025-09-24 12:43:13,482 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(680,) 
2025-09-24 12:43:13,520 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57485] 
2025-09-24 12:43:13,522 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [680] 
2025-09-24 12:43:13,522 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1942, 1941, 1940) 
2025-09-24 12:43:13,531 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57482, 57484, 57483] 
2025-09-24 12:43:13,531 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1942, 1941, 1940] 
2025-09-24 12:43:13,531 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(531, 530, 529) 
2025-09-24 12:43:13,564 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57479, 57480, 57481] 
2025-09-24 12:43:13,564 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [531, 530, 529] 
2025-09-24 12:43:13,564 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1939,) 
2025-09-24 12:43:13,577 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57478] 
2025-09-24 12:43:13,577 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1939] 
2025-09-24 12:43:13,577 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(528,) 
2025-09-24 12:43:13,593 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57477] 
2025-09-24 12:43:13,593 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [528] 
2025-09-24 12:43:13,593 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(679,) 
2025-09-24 12:43:13,609 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57476] 
2025-09-24 12:43:13,609 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [679] 
2025-09-24 12:43:13,609 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(527,) 
2025-09-24 12:43:13,616 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57475] 
2025-09-24 12:43:13,616 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [527] 
2025-09-24 12:43:13,625 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(678,) 
2025-09-24 12:43:13,629 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57474] 
2025-09-24 12:43:13,629 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [678] 
2025-09-24 12:43:13,642 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1938,) 
2025-09-24 12:43:13,643 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57473] 
2025-09-24 12:43:13,643 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1938] 
2025-09-24 12:43:13,654 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(526, 525, 524, 523, 522, 521) 
2025-09-24 12:43:13,673 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57465, 57471, 57472, 57470, 57469, 57468] 
2025-09-24 12:43:13,673 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [526, 525, 524, 523, 522, 521] 
2025-09-24 12:43:13,682 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields(11297,) 
2025-09-24 12:43:13,770 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57393] 
2025-09-24 12:43:13,770 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [11297] 
2025-09-24 12:43:13,777 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-09-24 12:43:13,784 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [71055, 57488, 57487, 57486, 57485, 57484, 57483, 57482, 57481, 57480, 57479, 57478, 57477, 57476, 57475, 57474, 57473, 57472, 57471, 57470, 57469, 57468, 57465, 57393, 57392, 57391] 
2025-09-24 12:43:13,791 127508 INFO kayan_whatsapp odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-09-24 12:43:13,833 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:43:13,846 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-24 12:43:13,882 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:43:13,895 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:43:15,061 127508 INFO kayan_whatsapp odoo.modules.loading: loading 97 modules... 
2025-09-24 12:43:15,120 127508 INFO kayan_whatsapp odoo.modules.loading: 97 modules loaded in 0.06s, 0 queries (+0 extra) 
2025-09-24 12:43:16,592 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:43:16,611 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:43:16,615 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 2.824s 
2025-09-24 12:43:16,622 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:43:16,625 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 5.948s 
2025-09-24 12:43:16,625 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 12:43:16,632 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:16] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 4575 3.007 2.980
2025-09-24 12:43:16,675 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:43:18,747 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:18] "GET /odoo HTTP/1.1" 200 - 98 0.083 1.994
2025-09-24 12:43:19,095 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:19] "GET /web/webclient/translations/36a2f370bcee97ce09ecc3fc8715f9ac9e38838e?lang=en_US HTTP/1.1" 200 - 1 0.002 0.010
2025-09-24 12:43:19,577 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:19] "GET /web/webclient/load_menus/2c82baff4300b560221f3bf3760b1b817f3586f0ed6d4bc01b41b54391fc8730 HTTP/1.1" 200 - 269 0.254 0.241
2025-09-24 12:43:47,649 127508 INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/a799e3d/web.assets_web.min.css (id:1145) 
2025-09-24 12:43:47,651 127508 INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/8c9ebb9/web.assets_web_print.min.css (id:1146) 
2025-09-24 12:43:47,653 127508 INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Deleting attachments [1136] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/a799e3d/web.assets_web.min.css 
2025-09-24 12:43:47,653 127508 INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Deleting attachments [1137] (matching /web/assets/_______/web.assets_web_print.min.css) because it was replaced with /web/assets/8c9ebb9/web.assets_web_print.min.css 
2025-09-24 12:43:47,725 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:47] "GET /web/assets/8c9ebb9/web.assets_web_print.min.css HTTP/1.1" 200 - 12 0.205 28.353
2025-09-24 12:43:47,750 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:47] "GET /web/assets/a799e3d/web.assets_web.min.css HTTP/1.1" 200 - 16 0.207 28.781
2025-09-24 12:43:48,180 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:48] "POST /web/action/load HTTP/1.1" 200 - 6 0.010 0.006
2025-09-24 12:43:48,246 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:48] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.016 0.013
2025-09-24 12:43:48,270 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:48] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.031 0.024
2025-09-24 12:43:48,484 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:48] "POST /mail/data HTTP/1.1" 200 - 69 0.168 0.103
2025-09-24 12:43:48,586 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:48] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.016 0.018
2025-09-24 12:43:48,630 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:48] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.018
2025-09-24 12:43:48,645 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:48] "POST /mail/data HTTP/1.1" 200 - 65 0.138 0.094
2025-09-24 12:43:48,719 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:48] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.002 0.011
2025-09-24 12:43:50,593 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:50] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.000
2025-09-24 12:43:53,430 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:53] "POST /web/action/load HTTP/1.1" 200 - 10 0.012 0.020
2025-09-24 12:43:53,922 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:53] "POST /web/dataset/call_kw/account.journal/get_views HTTP/1.1" 200 - 55 0.060 0.101
2025-09-24 12:43:54,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:54] "POST /web/dataset/call_kw/account.journal/web_search_read HTTP/1.1" 200 - 38 0.109 0.054
2025-09-24 12:43:54,252 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:54] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.002 0.007
2025-09-24 12:43:54,489 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 12:43:54,505 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.016s 
2025-09-24 12:43:54,524 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 12:43:54,533 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:54] "GET /web/bundle/web.chartjs_lib?lang=en_US&debug=1 HTTP/1.1" 200 - 2 0.000 0.012
2025-09-24 12:43:54,538 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 12:43:59,018 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:59] "POST /web/action/load HTTP/1.1" 200 - 9 0.004 0.022
2025-09-24 12:43:59,887 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:43:59] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 395 0.189 0.357
2025-09-24 12:44:00,075 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:00] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 204 0.069 0.092
2025-09-24 12:44:15,194 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:15] "POST /web/dataset/call_kw/account.journal/web_search_read HTTP/1.1" 200 - 26 0.023 0.057
2025-09-24 12:44:17,280 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:17] "POST /web/action/load HTTP/1.1" 200 - 10 0.001 0.002
2025-09-24 12:44:17,707 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:17] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 49 0.026 0.071
2025-09-24 12:44:17,869 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:17] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.006
2025-09-24 12:44:18,152 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:18] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.121
2025-09-24 12:44:18,160 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:18] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.100 0.033
2025-09-24 12:44:19,759 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:19] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.021 0.063
2025-09-24 12:44:20,079 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:20] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.019 0.065
2025-09-24 12:44:21,578 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:21] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.097 0.003
2025-09-24 12:44:21,992 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:21] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.011 0.252
2025-09-24 12:44:22,077 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /om_account_asset/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-09-24 12:44:22,382 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /om_account_budget/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-09-24 12:44:22,392 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /website_crm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-09-24 12:44:22,408 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /om_account_daily_reports/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.026
2025-09-24 12:44:22,408 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /om_recurring_payments/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.026
2025-09-24 12:44:22,413 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /base_account_budget/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.010
2025-09-24 12:44:22,423 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /account/static/description/l10n.png HTTP/1.1" 200 - 0 0.000 0.014
2025-09-24 12:44:22,715 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /muk_web_colors/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-09-24 12:44:22,715 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /muk_web_theme/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-09-24 12:44:22,732 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /om_account_followup/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.012
2025-09-24 12:44:22,748 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /website_customer/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.012
2025-09-24 12:44:22,749 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /web_unsplash/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.015
2025-09-24 12:44:22,749 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:22] "GET /partner_autocomplete/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.019
2025-09-24 12:44:23,030 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:23] "GET /website_google_map/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-09-24 12:44:23,036 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:23] "GET /website_sale_comparison/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-09-24 12:44:23,047 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:23] "GET /website_sale_loyalty/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-09-24 12:44:23,062 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:23] "GET /payment_custom/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-09-24 12:44:26,211 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:26] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.053 0.030
2025-09-24 12:44:26,414 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:26] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.000 0.034
2025-09-24 12:44:35,131 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['Odoo 18 Assets Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:44:35,131 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:35] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 3 0.002 0.012
2025-09-24 12:44:35,505 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:35] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 15 0.010 0.030
2025-09-24 12:44:35,779 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:35] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 13 0.033 0.019
2025-09-24 12:44:38,963 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:38] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 13 0.006 0.037
2025-09-24 12:44:39,294 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['Odoo 18 Assets Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:44:39,294 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-09-24 12:44:39,294 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['Odoo 18 Assets Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:44:39,352 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:44:39,361 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-24 12:44:39,394 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:44:39,394 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:44:40,538 127508 INFO kayan_whatsapp odoo.modules.loading: loading 97 modules... 
2025-09-24 12:44:40,629 127508 INFO kayan_whatsapp odoo.modules.loading: 97 modules loaded in 0.09s, 0 queries (+0 extra) 
2025-09-24 12:44:41,189 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['Odoo 18 Assets Management'] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:44:41,219 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(501,) 
2025-09-24 12:44:41,300 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\wizard\base_module_uninstall.py", line 63, in action_uninstall
    return modules.button_immediate_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 633, in button_immediate_uninstall
    return self._button_immediate_function(self.env.registry[self._name].button_uninstall)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 558, in load_modules
    Module.browse(modules_to_remove.values()).module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\account\models\ir_module.py", line 109, in module_uninstall
    return super().module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\base_import_module\models\ir_module.py", line 327, in module_uninstall
    res = super().module_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 494, in module_uninstall
    self.env['ir.model.data']._module_data_uninstall(modules_to_remove)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2522, in _module_data_uninstall
    delete(self.env[model].browse(item[1] for item in items))
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2510, in delete
    records.unlink()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_menu.py", line 199, in unlink
    return super(IrUiMenu, self).unlink()
  File "C:\odoo18_cubes\server\odoo\models.py", line 4491, in unlink
    self.modified(self._fields, before=True)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:44:41,329 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56031] 
2025-09-24 12:44:41,330 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [501] 
2025-09-24 12:44:41,333 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(655,) 
2025-09-24 12:44:41,382 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56030] 
2025-09-24 12:44:41,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [655] 
2025-09-24 12:44:41,386 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1890, 1889, 1888, 1887) 
2025-09-24 12:44:41,421 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56028, 56027, 56029, 56026] 
2025-09-24 12:44:41,421 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1890, 1889, 1888, 1887] 
2025-09-24 12:44:41,423 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(500,) 
2025-09-24 12:44:41,429 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56025] 
2025-09-24 12:44:41,429 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [500] 
2025-09-24 12:44:41,435 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(654,) 
2025-09-24 12:44:41,450 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56024] 
2025-09-24 12:44:41,450 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [654] 
2025-09-24 12:44:41,451 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1886, 1885, 1884, 1883) 
2025-09-24 12:44:41,470 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56021, 56020, 56023, 56022] 
2025-09-24 12:44:41,470 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1886, 1885, 1884, 1883] 
2025-09-24 12:44:41,470 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.asset(9,) 
2025-09-24 12:44:41,485 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56019] 
2025-09-24 12:44:41,485 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.asset records with IDs: [9] 
2025-09-24 12:44:41,487 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1882,) 
2025-09-24 12:44:41,501 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56018] 
2025-09-24 12:44:41,501 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1882] 
2025-09-24 12:44:41,503 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(499,) 
2025-09-24 12:44:41,516 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56017] 
2025-09-24 12:44:41,516 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [499] 
2025-09-24 12:44:41,518 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(653,) 
2025-09-24 12:44:41,541 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56016] 
2025-09-24 12:44:41,542 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [653] 
2025-09-24 12:44:41,544 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1881, 1880, 1879, 1878) 
2025-09-24 12:44:41,562 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56012, 56013, 56014, 56015] 
2025-09-24 12:44:41,562 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1881, 1880, 1879, 1878] 
2025-09-24 12:44:41,564 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(652,) 
2025-09-24 12:44:41,587 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56011] 
2025-09-24 12:44:41,587 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [652] 
2025-09-24 12:44:41,592 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1877,) 
2025-09-24 12:44:41,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56010] 
2025-09-24 12:44:41,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1877] 
2025-09-24 12:44:41,********** INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(498, 497) 
2025-09-24 12:44:41,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56009, 56008] 
2025-09-24 12:44:41,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [498, 497] 
2025-09-24 12:44:41,********** INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(651,) 
2025-09-24 12:44:41,659 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56007] 
2025-09-24 12:44:41,660 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [651] 
2025-09-24 12:44:41,662 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1876,) 
2025-09-24 12:44:41,679 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56006] 
2025-09-24 12:44:41,679 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1876] 
2025-09-24 12:44:41,683 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.access(1016, 1015, 1014, 1013, 1012, 1011, 1010, 1009, 1008, 1007, 1006, 1005, 1004) 
2025-09-24 12:44:41,700 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [55996, 56004, 55998, 55993, 56003, 55997, 55999, 56005, 56000, 56001, 56002, 55994, 55995] 
2025-09-24 12:44:41,701 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.access records with IDs: [1016, 1015, 1014, 1013, 1012, 1011, 1010, 1009, 1008, 1007, 1006, 1005, 1004] 
2025-09-24 12:44:41,703 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.rule(259, 258) 
2025-09-24 12:44:41,720 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [55992, 55991] 
2025-09-24 12:44:41,720 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.rule records with IDs: [259, 258] 
2025-09-24 12:44:41,725 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.cron(48,) 
2025-09-24 12:44:41,750 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [55990] 
2025-09-24 12:44:41,751 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.cron records with IDs: [48] 
2025-09-24 12:44:41,754 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.server(650,) 
2025-09-24 12:44:41,788 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [55989] 
2025-09-24 12:44:41,788 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.server records with IDs: [650] 
2025-09-24 12:44:41,789 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.inherit(240, 239, 237, 236) 
2025-09-24 12:44:41,809 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [55968, 55967, 55959, 55958] 
2025-09-24 12:44:41,809 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.inherit records with IDs: [240, 239, 237, 236] 
2025-09-24 12:44:41,814 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields.selection(2621, 2620, 2619, 2618, 2617, 2616, 2615, 2614, 2613, 2612, 2611, 2610, 2604, 2603, 2602, 2601, 2600, 2599, 2598, 2597) 
2025-09-24 12:44:41,829 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [55894, 55895, 55891, 55890, 55893, 55892, 55889, 55887, 55888, 55880, 55881, 55875, 55874, 55877, 55876, 55879, 55878, 55932, 55930, 55931] 
2025-09-24 12:44:41,829 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [2621, 2620, 2619, 2618, 2617, 2616, 2615, 2614, 2613, 2612, 2611, 2610, 2604, 2603, 2602, 2601, 2600, 2599, 2598, 2597] 
2025-09-24 12:44:41,856 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields(11020, 11019, 11018, 11017, 11016, 11014, 11013, 11012, 11011, 11010, 11009, 11008, 11007, 11006, 11005, 11004, 11003, 11002, 11001, 11000, 10999, 10998, 10997, 10996, 10995, 10994, 10993, 10988, 10986, 10985, 10984, 10983, 10982, 10981, 10980, 10979, 10978, 10977, 10976, 10975, 10970, 10968, 10967, 10966, 10965, 10964, 10963, 10962, 10961, 10960, 10959, 10958, 10957, 10956, 10955, 10954, 10953, 10952, 10951, 10950, 10949, 10948, 10947, 10946, 10945, 10944, 10943, 10942, 10941, 10940, 10939, 10938, 10937, 10936, 10935, 10934, 10933, 10932, 10931, 10930, 10929, 10928, 10927, 10926, 10925, 10924, 10923, 10922, 10921, 10920, 10919, 10918, 10917, 10916, 10911, 10909, 10908, 10907, 10906, 10905, 10904, 10903, 10902, 10901, 10900, 10899, 10898, 10897, 10896, 10895, 10894, 10893, 10892, 10891, 10890, 10889, 10888, 10887, 10886, 10885, 10884, 10883, 10882, 10881, 10880, 10879, 10878, 10877, 10876, 10875, 10874, 10873, 10872, 10871, 10870, 10869, 10868, 10867, 10866, 10865, 10864, 10863, 10858, 10856, 10855, 10854, 10853, 10852, 10847, 10845) 
2025-09-24 12:44:43,090 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [55776, 55762, 55734, 55737, 55738, 55729, 55730, 55736, 55733, 55732, 55731, 55726, 55727, 55759, 55754, 55757, 55756, 55760, 55777, 55772, 55780, 55728, 55752, 55778, 55743, 55774, 55748, 55740, 55746, 55747, 55751, 55742, 55739, 55744, 55745, 55741, 55764, 55767, 55765, 55766, 55768, 55770, 55735, 55753, 55758, 55763, 55771, 55749, 55773, 55761, 55775, 55755, 55769, 55750, 55703, 55704, 55706, 55705, 55701, 55683, 55686, 55687, 55678, 55679, 55685, 55682, 55681, 55680, 55675, 55676, 55708, 55719, 55721, 55677, 55717, 55692, 55707, 55697, 55689, 55695, 55696, 55700, 55691, 55688, 55693, 55694, 55690, 55709, 55714, 55710, 55711, 55712, 55713, 55684, 55702, 55716, 55715, 55698, 55718, 55699, 55789, 55787, 55796, 55791, 55792, 55798, 55794, 55793, 55795, 55785, 55788, 55790, 55786, 55827, 55828, 55673, 55674, 55803, 55805, 55806, 55804, 55813, 55812, 55824, 55810, 55811, 55820, 55816, 55826, 55821, 55819, 55817, 55818, 55809, 55814, 55822, 55815, 55823, 55655, 55657, 55666, 55668, 55665, 55663, 55664, 55662, 55829, 55830, 55807, 55808] 
2025-09-24 12:44:43,090 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [11020, 11019, 11018, 11017, 11016, 11014, 11013, 11012, 11011, 11010, 11009, 11008, 11007, 11006, 11005, 11004, 11003, 11002, 11001, 11000, 10999, 10998, 10997, 10996, 10995, 10994, 10993, 10988, 10986, 10985, 10984, 10983, 10982, 10981, 10980, 10979, 10978, 10977, 10976, 10975, 10970, 10968, 10967, 10966, 10965, 10964, 10963, 10962, 10961, 10960, 10959, 10958, 10957, 10956, 10955, 10954, 10953, 10952, 10951, 10950, 10949, 10948, 10947, 10946, 10945, 10944, 10943, 10942, 10941, 10940, 10939, 10938, 10937, 10936, 10935, 10934, 10933, 10932, 10931, 10930, 10929, 10928, 10927, 10926, 10925, 10924, 10923, 10922, 10921, 10920, 10919, 10918, 10917, 10916, 10911, 10909, 10908, 10907, 10906, 10905, 10904, 10903, 10902, 10901, 10900, 10899, 10898, 10897, 10896, 10895, 10894, 10893, 10892, 10891, 10890, 10889, 10888, 10887, 10886, 10885, 10884, 10883, 10882, 10881, 10880, 10879, 10878, 10877, 10876, 10875, 10874, 10873, 10872, 10871, 10870, 10869, 10868, 10867, 10866, 10865, 10864, 10863, 10858, 10856, 10855, 10854, 10853, 10852, 10847, 10845] 
2025-09-24 12:44:43,092 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model(759, 758, 757, 756, 755, 754) 
2025-09-24 12:44:43,282 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [55652, 55651, 55653, 55654, 55649, 55650] 
2025-09-24 12:44:43,283 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model records with IDs: [759, 758, 757, 756, 755, 754] 
2025-09-24 12:44:43,283 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-09-24 12:44:43,298 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56031, 56030, 56029, 56028, 56027, 56026, 56025, 56024, 56023, 56022, 56021, 56020, 56019, 56018, 56017, 56016, 56015, 56014, 56013, 56012, 56011, 56010, 56009, 56008, 56007, 56006, 56005, 56004, 56003, 56002, 56001, 56000, 55999, 55998, 55997, 55996, 55995, 55994, 55993, 55992, 55991, 55990, 55989, 55968, 55967, 55966, 55959, 55958, 55957, 55932, 55931, 55930, 55895, 55894, 55893, 55892, 55891, 55890, 55889, 55888, 55887, 55881, 55880, 55879, 55878, 55877, 55876, 55875, 55874, 55830, 55829, 55828, 55827, 55826, 55825, 55824, 55823, 55822, 55821, 55820, 55819, 55818, 55817, 55816, 55815, 55814, 55813, 55812, 55811, 55810, 55809, 55808, 55807, 55806, 55805, 55804, 55803, 55802, 55801, 55800, 55799, 55798, 55797, 55796, 55795, 55794, 55793, 55792, 55791, 55790, 55789, 55788, 55787, 55786, 55785, 55784, 55783, 55782, 55781, 55780, 55779, 55778, 55777, 55776, 55775, 55774, 55773, 55772, 55771, 55770, 55769, 55768, 55767, 55766, 55765, 55764, 55763, 55762, 55761, 55760, 55759, 55758, 55757, 55756, 55755, 55754, 55753, 55752, 55751, 55750, 55749, 55748, 55747, 55746, 55745, 55744, 55743, 55742, 55741, 55740, 55739, 55738, 55737, 55736, 55735, 55734, 55733, 55732, 55731, 55730, 55729, 55728, 55727, 55726, 55725, 55724, 55723, 55722, 55721, 55720, 55719, 55718, 55717, 55716, 55715, 55714, 55713, 55712, 55711, 55710, 55709, 55708, 55707, 55706, 55705, 55704, 55703, 55702, 55701, 55700, 55699, 55698, 55697, 55696, 55695, 55694, 55693, 55692, 55691, 55690, 55689, 55688, 55687, 55686, 55685, 55684, 55683, 55682, 55681, 55680, 55679, 55678, 55677, 55676, 55675, 55674, 55673, 55672, 55671, 55670, 55669, 55668, 55667, 55666, 55665, 55664, 55663, 55662, 55661, 55660, 55659, 55658, 55657, 55656, 55655, 55654, 55653, 55652, 55651, 55650, 55649, 55648, 55647, 55646] 
2025-09-24 12:44:43,342 127508 INFO kayan_whatsapp odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-09-24 12:44:43,361 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:44:43,********** INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-24 12:44:43,416 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:44:43,421 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:44:44,456 127508 INFO kayan_whatsapp odoo.modules.loading: loading 96 modules... 
2025-09-24 12:44:44,554 127508 INFO kayan_whatsapp odoo.modules.loading: 96 modules loaded in 0.10s, 0 queries (+0 extra) 
2025-09-24 12:44:46,704 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:44:46,710 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:44:46,711 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 3.369s 
2025-09-24 12:44:46,720 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:44:46,722 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 7.393s 
2025-09-24 12:44:46,723 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 12:44:46,730 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:46] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 4979 4.226 3.210
2025-09-24 12:44:46,763 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:44:48,558 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:48] "GET /odoo HTTP/1.1" 200 - 97 0.102 1.700
2025-09-24 12:44:48,855 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:48] "GET /web/webclient/load_menus/bd9a94dbc6bb84c5bd4570c174ee38ddb23ae442a9faf3436f908309e68a8fbd HTTP/1.1" 200 - 265 0.153 0.130
2025-09-24 12:44:48,888 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:48] "GET /web/webclient/translations/d50e0a14e58041db924b9d45c930f50cda17837f?lang=en_US HTTP/1.1" 200 - 1 0.002 0.007
2025-09-24 12:44:49,046 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:49] "POST /web/action/load HTTP/1.1" 200 - 6 0.005 0.013
2025-09-24 12:44:49,313 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:49] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.045 0.019
2025-09-24 12:44:49,377 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:49] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.100 0.028
2025-09-24 12:44:49,425 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:49] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.000 0.048
2025-09-24 12:44:49,441 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:49] "POST /mail/data HTTP/1.1" 200 - 69 0.168 0.060
2025-09-24 12:44:49,479 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:49] "POST /mail/data HTTP/1.1" 200 - 65 0.128 0.108
2025-09-24 12:44:49,754 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:49] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.002 0.012
2025-09-24 12:44:49,862 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:49] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.007 0.010
2025-09-24 12:44:51,104 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:51] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.007 0.000
2025-09-24 12:44:53,751 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:53] "POST /web/action/load HTTP/1.1" 200 - 10 0.015 0.009
2025-09-24 12:44:54,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:54] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.036 0.094
2025-09-24 12:44:54,135 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:54] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.006
2025-09-24 12:44:54,519 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:54] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.045 0.030
2025-09-24 12:44:54,527 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:54] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.000 0.083
2025-09-24 12:44:59,078 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:59] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.047 0.035
2025-09-24 12:44:59,097 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:59] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.016 0.086
2025-09-24 12:44:59,844 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:44:59] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.061 0.054
2025-09-24 12:45:00,227 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:00] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.017 0.055
2025-09-24 12:45:03,381 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:03] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.057 0.036
2025-09-24 12:45:03,733 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:03] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.000 0.020
2025-09-24 12:45:14,288 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['Odoo 18 Budget Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:45:14,288 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:14] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 3 0.005 0.015
2025-09-24 12:45:14,667 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:14] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 15 0.027 0.024
2025-09-24 12:45:14,918 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:14] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 11 0.025 0.015
2025-09-24 12:45:16,713 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:16] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 12 0.013 0.029
2025-09-24 12:45:17,061 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['Odoo 18 Budget Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:45:17,061 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-09-24 12:45:17,061 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['Odoo 18 Budget Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:45:17,110 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:45:17,111 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-24 12:45:17,144 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:45:17,144 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:45:18,585 127508 INFO kayan_whatsapp odoo.modules.loading: loading 96 modules... 
2025-09-24 12:45:18,640 127508 INFO kayan_whatsapp odoo.modules.loading: 96 modules loaded in 0.06s, 0 queries (+0 extra) 
2025-09-24 12:45:19,159 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['Odoo 18 Budget Management'] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:45:19,181 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting crossovered.budget.lines(26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14) 
2025-09-24 12:45:19,241 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\wizard\base_module_uninstall.py", line 63, in action_uninstall
    return modules.button_immediate_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 633, in button_immediate_uninstall
    return self._button_immediate_function(self.env.registry[self._name].button_uninstall)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 558, in load_modules
    Module.browse(modules_to_remove.values()).module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\account\models\ir_module.py", line 109, in module_uninstall
    return super().module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\base_import_module\models\ir_module.py", line 327, in module_uninstall
    res = super().module_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 494, in module_uninstall
    self.env['ir.model.data']._module_data_uninstall(modules_to_remove)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2522, in _module_data_uninstall
    delete(self.env[model].browse(item[1] for item in items))
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2510, in delete
    records.unlink()
  File "C:\odoo18_cubes\server\odoo\models.py", line 4491, in unlink
    self.modified(self._fields, before=True)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:45:19,276 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56160, 56161, 56170, 56171, 56172, 56162, 56163, 56164, 56165, 56166, 56167, 56168, 56169] 
2025-09-24 12:45:19,276 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted crossovered.budget.lines records with IDs: [26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14] 
2025-09-24 12:45:19,276 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting crossovered.budget(2,) 
2025-09-24 12:45:19,350 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted mail.message records with IDs: [633] 
2025-09-24 12:45:19,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56159] 
2025-09-24 12:45:19,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted crossovered.budget records with IDs: [2] 
2025-09-24 12:45:19,372 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting crossovered.budget.lines(13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1) 
2025-09-24 12:45:19,381 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56146, 56147, 56156, 56157, 56158, 56148, 56149, 56150, 56151, 56152, 56153, 56154, 56155] 
2025-09-24 12:45:19,381 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted crossovered.budget.lines records with IDs: [13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1] 
2025-09-24 12:45:19,381 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting crossovered.budget(1,) 
2025-09-24 12:45:19,402 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted mail.message records with IDs: [632] 
2025-09-24 12:45:19,413 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56145] 
2025-09-24 12:45:19,414 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted crossovered.budget records with IDs: [1] 
2025-09-24 12:45:19,414 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1904,) 
2025-09-24 12:45:19,429 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56144] 
2025-09-24 12:45:19,429 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1904] 
2025-09-24 12:45:19,429 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(659,) 
2025-09-24 12:45:19,445 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56143] 
2025-09-24 12:45:19,445 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [659] 
2025-09-24 12:45:19,460 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(504,) 
2025-09-24 12:45:19,466 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56142] 
2025-09-24 12:45:19,466 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [504] 
2025-09-24 12:45:19,470 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(658,) 
2025-09-24 12:45:19,476 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56141] 
2025-09-24 12:45:19,476 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [658] 
2025-09-24 12:45:19,486 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1903, 1902, 1901, 1900, 1899) 
2025-09-24 12:45:19,505 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56138, 56140, 56139, 56136, 56137] 
2025-09-24 12:45:19,505 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1903, 1902, 1901, 1900, 1899] 
2025-09-24 12:45:19,507 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(503,) 
2025-09-24 12:45:19,513 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56135] 
2025-09-24 12:45:19,513 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [503] 
2025-09-24 12:45:19,513 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(657,) 
2025-09-24 12:45:19,523 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56134] 
2025-09-24 12:45:19,523 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [657] 
2025-09-24 12:45:19,523 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1898, 1897, 1896, 1895, 1894) 
2025-09-24 12:45:19,539 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56130, 56131, 56129, 56132, 56133] 
2025-09-24 12:45:19,539 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1898, 1897, 1896, 1895, 1894] 
2025-09-24 12:45:19,539 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(502,) 
2025-09-24 12:45:19,555 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56128] 
2025-09-24 12:45:19,555 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [502] 
2025-09-24 12:45:19,555 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(656,) 
2025-09-24 12:45:19,561 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56127] 
2025-09-24 12:45:19,561 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [656] 
2025-09-24 12:45:19,561 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1893, 1892, 1891) 
2025-09-24 12:45:19,576 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56124, 56125, 56126] 
2025-09-24 12:45:19,576 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1893, 1892, 1891] 
2025-09-24 12:45:19,576 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.rule(262, 261, 260) 
2025-09-24 12:45:19,586 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56120, 56121, 56119] 
2025-09-24 12:45:19,586 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.rule records with IDs: [262, 261, 260] 
2025-09-24 12:45:19,586 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.access(1022, 1021, 1020, 1019, 1018, 1017) 
2025-09-24 12:45:19,593 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56114, 56115, 56118, 56113, 56116, 56117] 
2025-09-24 12:45:19,593 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.access records with IDs: [1022, 1021, 1020, 1019, 1018, 1017] 
2025-09-24 12:45:19,603 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields.selection(2626, 2625, 2624, 2623, 2622) 
2025-09-24 12:45:19,618 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56095, 56096, 56098, 56094, 56097] 
2025-09-24 12:45:19,618 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [2626, 2625, 2624, 2623, 2622] 
2025-09-24 12:45:19,618 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields(11078, 11073, 11071, 11070, 11069, 11068, 11067, 11066, 11065, 11064, 11063, 11062, 11061, 11060, 11059, 11058, 11057, 11056, 11051, 11049, 11048, 11047, 11046, 11045, 11044, 11043, 11042, 11041, 11040, 11039, 11038, 11037, 11036, 11035, 11034, 11033, 11032, 11031, 11030, 11025, 11023, 11022, 11021) 
2025-09-24 12:45:19,903 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56093, 56037, 56038, 56040, 56036, 56064, 56063, 56060, 56061, 56066, 56049, 56054, 56046, 56052, 56053, 56057, 56048, 56045, 56050, 56051, 56047, 56058, 56055, 56062, 56059, 56056, 56073, 56074, 56084, 56072, 56086, 56079, 56076, 56077, 56088, 56075, 56085, 56071, 56078, 56083, 56080, 56081, 56082] 
2025-09-24 12:45:19,903 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [11078, 11073, 11071, 11070, 11069, 11068, 11067, 11066, 11065, 11064, 11063, 11062, 11061, 11060, 11059, 11058, 11057, 11056, 11051, 11049, 11048, 11047, 11046, 11045, 11044, 11043, 11042, 11041, 11040, 11039, 11038, 11037, 11036, 11035, 11034, 11033, 11032, 11031, 11030, 11025, 11023, 11022, 11021] 
2025-09-24 12:45:19,903 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.relation records with IDs: [208] 
2025-09-24 12:45:19,925 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Dropped table account_budget_rel 
2025-09-24 12:45:19,925 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model(762, 761, 760) 
2025-09-24 12:45:20,047 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56033, 56034, 56035] 
2025-09-24 12:45:20,047 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model records with IDs: [762, 761, 760] 
2025-09-24 12:45:20,047 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-09-24 12:45:20,069 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [56172, 56171, 56170, 56169, 56168, 56167, 56166, 56165, 56164, 56163, 56162, 56161, 56160, 56159, 56158, 56157, 56156, 56155, 56154, 56153, 56152, 56151, 56150, 56149, 56148, 56147, 56146, 56145, 56144, 56143, 56142, 56141, 56140, 56139, 56138, 56137, 56136, 56135, 56134, 56133, 56132, 56131, 56130, 56129, 56128, 56127, 56126, 56125, 56124, 56121, 56120, 56119, 56118, 56117, 56116, 56115, 56114, 56113, 56105, 56098, 56097, 56096, 56095, 56094, 56093, 56092, 56091, 56090, 56089, 56088, 56087, 56086, 56085, 56084, 56083, 56082, 56081, 56080, 56079, 56078, 56077, 56076, 56075, 56074, 56073, 56072, 56071, 56070, 56069, 56068, 56067, 56066, 56065, 56064, 56063, 56062, 56061, 56060, 56059, 56058, 56057, 56056, 56055, 56054, 56053, 56052, 56051, 56050, 56049, 56048, 56047, 56046, 56045, 56044, 56043, 56042, 56041, 56040, 56039, 56038, 56037, 56036, 56035, 56034, 56033, 56032] 
2025-09-24 12:45:20,094 127508 INFO kayan_whatsapp odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-09-24 12:45:20,113 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:45:20,316 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.20s, 0 queries (+0 extra) 
2025-09-24 12:45:20,332 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:45:20,348 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:45:21,637 127508 INFO kayan_whatsapp odoo.modules.loading: loading 95 modules... 
2025-09-24 12:45:21,747 127508 INFO kayan_whatsapp odoo.modules.loading: 95 modules loaded in 0.11s, 0 queries (+0 extra) 
2025-09-24 12:45:23,677 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:45:23,********** INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:45:23,********** INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 3.600s 
2025-09-24 12:45:23,********** INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:45:23,********** INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 6.616s 
2025-09-24 12:45:23,********** INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 12:45:23,713 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:23] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 4752 3.662 3.007
2025-09-24 12:45:23,762 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:45:25,286 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:25] "GET /odoo HTTP/1.1" 200 - 97 0.162 1.369
2025-09-24 12:45:25,637 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:25] "GET /web/webclient/translations/10d2a99260eae5e6c97b631d5986e3655212fa7d?lang=en_US HTTP/1.1" 200 - 1 0.002 0.017
2025-09-24 12:45:25,769 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:25] "GET /web/webclient/load_menus/0e959fa13ab83faf5a68c2b27c4cb84d53656b07225aff8c8b1697dc6127af95 HTTP/1.1" 200 - 262 0.249 0.215
2025-09-24 12:45:25,909 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:25] "POST /mail/data HTTP/1.1" 200 - 69 0.043 0.056
2025-09-24 12:45:25,975 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:25] "POST /web/action/load HTTP/1.1" 200 - 6 0.003 0.009
2025-09-24 12:45:25,993 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:25] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.011 0.005
2025-09-24 12:45:26,210 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:26] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.003 0.043
2025-09-24 12:45:26,343 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:26] "POST /mail/data HTTP/1.1" 200 - 65 0.093 0.086
2025-09-24 12:45:26,375 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:26] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.009 0.007
2025-09-24 12:45:26,830 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:26] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.001 0.017
2025-09-24 12:45:26,879 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:26] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.017
2025-09-24 12:45:28,265 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:28] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.000
2025-09-24 12:45:33,937 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:33] "POST /web/action/load HTTP/1.1" 200 - 10 0.004 0.009
2025-09-24 12:45:34,463 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:34] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.039 0.240
2025-09-24 12:45:34,476 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:34] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.002 0.002
2025-09-24 12:45:34,849 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:34] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.062 0.010
2025-09-24 12:45:34,859 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:34] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.002 0.079
2025-09-24 12:45:36,328 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:36] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.039 0.025
2025-09-24 12:45:36,353 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:36] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.007 0.083
2025-09-24 12:45:38,699 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:38] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.033 0.043
2025-09-24 12:45:39,068 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:39] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.010 0.048
2025-09-24 12:45:44,711 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.036 0.036
2025-09-24 12:45:45,048 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.016
2025-09-24 12:45:55,577 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['Cash Book, Day Book, Bank Book Financial Reports'] to user admin #2 via 127.0.0.1 
2025-09-24 12:45:55,578 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:55] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 3 0.002 0.006
2025-09-24 12:45:55,928 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:55] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 15 0.013 0.019
2025-09-24 12:45:56,173 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:56] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 9 0.015 0.010
2025-09-24 12:45:57,787 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:45:57] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 11 0.015 0.008
2025-09-24 12:45:58,108 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['Cash Book, Day Book, Bank Book Financial Reports'] to user admin #2 via 127.0.0.1 
2025-09-24 12:45:58,109 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-09-24 12:45:58,110 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['Cash Book, Day Book, Bank Book Financial Reports'] to user admin #2 via 127.0.0.1 
2025-09-24 12:45:58,144 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:45:58,154 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-24 12:45:58,187 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:45:58,187 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:45:59,********** INFO kayan_whatsapp odoo.modules.loading: loading 95 modules... 
2025-09-24 12:45:59,229 127508 INFO kayan_whatsapp odoo.modules.loading: 95 modules loaded in 0.10s, 0 queries (+0 extra) 
2025-09-24 12:45:59,788 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['Cash Book, Day Book, Bank Book Financial Reports'] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:45:59,798 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1937, 1936, 1935) 
2025-09-24 12:45:59,********** WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\wizard\base_module_uninstall.py", line 63, in action_uninstall
    return modules.button_immediate_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 633, in button_immediate_uninstall
    return self._button_immediate_function(self.env.registry[self._name].button_uninstall)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 558, in load_modules
    Module.browse(modules_to_remove.values()).module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\account\models\ir_module.py", line 109, in module_uninstall
    return super().module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\base_import_module\models\ir_module.py", line 327, in module_uninstall
    res = super().module_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 494, in module_uninstall
    self.env['ir.model.data']._module_data_uninstall(modules_to_remove)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2522, in _module_data_uninstall
    delete(self.env[model].browse(item[1] for item in items))
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2510, in delete
    records.unlink()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 557, in unlink
    return super(View, self).unlink()
  File "C:\odoo18_cubes\server\odoo\models.py", line 4491, in unlink
    self.modified(self._fields, before=True)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:45:59,864 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57390, 57389, 57388] 
2025-09-24 12:45:59,864 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1937, 1936, 1935] 
2025-09-24 12:45:59,864 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.report(677, 676, 675) 
2025-09-24 12:45:59,888 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57387, 57386, 57385] 
2025-09-24 12:45:59,888 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.report records with IDs: [677, 676, 675] 
2025-09-24 12:45:59,893 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(520,) 
2025-09-24 12:45:59,899 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57384] 
2025-09-24 12:45:59,903 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [520] 
2025-09-24 12:45:59,903 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(674,) 
2025-09-24 12:45:59,917 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57383] 
2025-09-24 12:45:59,917 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [674] 
2025-09-24 12:45:59,917 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1934,) 
2025-09-24 12:45:59,923 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57382] 
2025-09-24 12:45:59,923 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1934] 
2025-09-24 12:45:59,928 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(519,) 
2025-09-24 12:45:59,934 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57381] 
2025-09-24 12:45:59,934 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [519] 
2025-09-24 12:45:59,938 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(673,) 
2025-09-24 12:45:59,947 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57380] 
2025-09-24 12:45:59,947 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [673] 
2025-09-24 12:45:59,949 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1933,) 
2025-09-24 12:45:59,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57379] 
2025-09-24 12:45:59,958 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1933] 
2025-09-24 12:45:59,958 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(518,) 
2025-09-24 12:45:59,964 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57378] 
2025-09-24 12:45:59,964 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [518] 
2025-09-24 12:45:59,964 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(672,) 
2025-09-24 12:45:59,973 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57377] 
2025-09-24 12:45:59,973 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [672] 
2025-09-24 12:45:59,973 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1932,) 
2025-09-24 12:45:59,983 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57376] 
2025-09-24 12:45:59,983 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1932] 
2025-09-24 12:45:59,983 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(517,) 
2025-09-24 12:45:59,994 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57375] 
2025-09-24 12:45:59,994 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [517] 
2025-09-24 12:45:59,994 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.access(1041, 1040, 1039) 
2025-09-24 12:45:59,995 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57374, 57373, 57372] 
2025-09-24 12:45:59,995 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.access records with IDs: [1041, 1040, 1039] 
2025-09-24 12:46:00,003 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields.selection(2656, 2655, 2654, 2653, 2652, 2651, 2650, 2649, 2648, 2647, 2646, 2645, 2644, 2643, 2642, 2641) 
2025-09-24 12:46:00,018 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57367, 57368, 57369, 57370, 57371, 57366, 57365, 57360, 57361, 57362, 57363, 57364, 57359, 57358, 57357, 57356] 
2025-09-24 12:46:00,018 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [2656, 2655, 2654, 2653, 2652, 2651, 2650, 2649, 2648, 2647, 2646, 2645, 2644, 2643, 2642, 2641] 
2025-09-24 12:46:00,023 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields(11292, 11290, 11289, 11288, 11287, 11286, 11285, 11284, 11283, 11278, 11276, 11275, 11274, 11273, 11272, 11271, 11270, 11269, 11264, 11262, 11261, 11260, 11259, 11258) 
2025-09-24 12:46:00,194 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57346, 57342, 57343, 57347, 57351, 57349, 57345, 57348, 57344, 57332, 57328, 57329, 57333, 57337, 57335, 57331, 57334, 57330, 57321, 57317, 57318, 57323, 57320, 57319] 
2025-09-24 12:46:00,194 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [11292, 11290, 11289, 11288, 11287, 11286, 11285, 11284, 11283, 11278, 11276, 11275, 11274, 11273, 11272, 11271, 11270, 11269, 11264, 11262, 11261, 11260, 11259, 11258] 
2025-09-24 12:46:00,203 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.relation records with IDs: [210, 211, 212, 213, 214, 215] 
2025-09-24 12:46:00,209 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Dropped table account_account_bankbook_report 
2025-09-24 12:46:00,216 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Dropped table account_bankbook_report_account_journal_rel 
2025-09-24 12:46:00,228 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Dropped table account_account_cashbook_report 
2025-09-24 12:46:00,233 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Dropped table account_cashbook_report_account_journal_rel 
2025-09-24 12:46:00,243 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Dropped table account_account_daybook_report 
2025-09-24 12:46:00,249 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Dropped table account_daybook_report_account_journal_rel 
2025-09-24 12:46:00,249 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model(780, 779, 778, 777, 776, 775) 
2025-09-24 12:46:00,354 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57313, 57312, 57311, 57316, 57315, 57314] 
2025-09-24 12:46:00,354 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model records with IDs: [780, 779, 778, 777, 776, 775] 
2025-09-24 12:46:00,354 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-09-24 12:46:00,363 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57390, 57389, 57388, 57387, 57386, 57385, 57384, 57383, 57382, 57381, 57380, 57379, 57378, 57377, 57376, 57375, 57374, 57373, 57372, 57371, 57370, 57369, 57368, 57367, 57366, 57365, 57364, 57363, 57362, 57361, 57360, 57359, 57358, 57357, 57356, 57355, 57354, 57353, 57352, 57351, 57350, 57349, 57348, 57347, 57346, 57345, 57344, 57343, 57342, 57341, 57340, 57339, 57338, 57337, 57336, 57335, 57334, 57333, 57332, 57331, 57330, 57329, 57328, 57327, 57326, 57325, 57324, 57323, 57322, 57321, 57320, 57319, 57318, 57317, 57316, 57315, 57314, 57313, 57312, 57311] 
2025-09-24 12:46:00,394 127508 INFO kayan_whatsapp odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-09-24 12:46:00,406 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:46:00,418 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-24 12:46:00,448 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:46:00,453 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:46:01,439 127508 INFO kayan_whatsapp odoo.modules.loading: loading 94 modules... 
2025-09-24 12:46:01,733 127508 INFO kayan_whatsapp odoo.modules.loading: 94 modules loaded in 0.29s, 0 queries (+0 extra) 
2025-09-24 12:46:03,203 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:46:03,209 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:46:03,214 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 2.815s 
2025-09-24 12:46:03,219 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:46:03,219 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 5.085s 
2025-09-24 12:46:03,219 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 12:46:03,223 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:03] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 4667 2.234 2.890
2025-09-24 12:46:03,254 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:46:04,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:04] "GET /odoo HTTP/1.1" 200 - 97 0.064 1.383
2025-09-24 12:46:05,008 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:05] "GET /web/webclient/load_menus/ff4d9b1893b36acc34ad02e9dba008897e86abdfee096e5962f560c41ac8dddc HTTP/1.1" 200 - 259 0.128 0.159
2025-09-24 12:46:05,035 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:05] "GET /web/webclient/translations/5693dcfa24844530c18229a78158db2ce172f382?lang=en_US HTTP/1.1" 200 - 1 0.001 0.008
2025-09-24 12:46:05,323 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:05] "POST /web/action/load HTTP/1.1" 200 - 6 0.000 0.013
2025-09-24 12:46:05,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:05] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.038 0.048
2025-09-24 12:46:05,554 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:05] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.049 0.052
2025-09-24 12:46:05,587 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:05] "POST /mail/data HTTP/1.1" 200 - 69 0.148 0.090
2025-09-24 12:46:05,655 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:05] "POST /mail/data HTTP/1.1" 200 - 65 0.133 0.079
2025-09-24 12:46:05,703 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:05] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.003 0.006
2025-09-24 12:46:06,048 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:06] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.005 0.005
2025-09-24 12:46:06,089 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:06] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.009
2025-09-24 12:46:07,437 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:07] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.002 0.004
2025-09-24 12:46:41,513 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:41] "POST /web/action/load HTTP/1.1" 200 - 10 0.018 0.001
2025-09-24 12:46:41,884 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:41] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.023 0.104
2025-09-24 12:46:41,901 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:41] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.002 0.005
2025-09-24 12:46:42,311 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:42] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.010 0.089
2025-09-24 12:46:42,311 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:42] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.073 0.027
2025-09-24 12:46:43,845 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:43] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.040 0.060
2025-09-24 12:46:43,878 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:43] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.017 0.116
2025-09-24 12:46:46,778 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:46] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.067 0.044
2025-09-24 12:46:47,189 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:47] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.017 0.075
2025-09-24 12:46:50,941 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:50] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.031 0.050
2025-09-24 12:46:51,301 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:46:51] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.031
2025-09-24 12:46:53,309 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:46:53,323 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.015s 
2025-09-24 12:46:53,329 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:46:53,332 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:47:03,797 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['Odoo 18 Fiscal Year & Lock Date'] to user admin #2 via 127.0.0.1 
2025-09-24 12:47:03,800 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:03] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 3 0.005 0.016
2025-09-24 12:47:04,180 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:04] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 15 0.014 0.036
2025-09-24 12:47:04,431 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:04] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 9 0.020 0.017
2025-09-24 12:47:05,897 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:05] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 11 0.003 0.038
2025-09-24 12:47:06,230 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['Odoo 18 Fiscal Year & Lock Date'] to user admin #2 via 127.0.0.1 
2025-09-24 12:47:06,230 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-09-24 12:47:06,230 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['Odoo 18 Fiscal Year & Lock Date'] to user admin #2 via 127.0.0.1 
2025-09-24 12:47:06,278 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:47:06,278 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-24 12:47:06,330 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:47:06,330 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:47:07,881 127508 INFO kayan_whatsapp odoo.modules.loading: loading 94 modules... 
2025-09-24 12:47:07,960 127508 INFO kayan_whatsapp odoo.modules.loading: 94 modules loaded in 0.08s, 0 queries (+0 extra) 
2025-09-24 12:47:08,913 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['Odoo 18 Fiscal Year & Lock Date'] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:47:08,926 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1927,) 
2025-09-24 12:47:08,980 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\wizard\base_module_uninstall.py", line 63, in action_uninstall
    return modules.button_immediate_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 633, in button_immediate_uninstall
    return self._button_immediate_function(self.env.registry[self._name].button_uninstall)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 558, in load_modules
    Module.browse(modules_to_remove.values()).module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\account\models\ir_module.py", line 109, in module_uninstall
    return super().module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\base_import_module\models\ir_module.py", line 327, in module_uninstall
    res = super().module_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 494, in module_uninstall
    self.env['ir.model.data']._module_data_uninstall(modules_to_remove)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2522, in _module_data_uninstall
    delete(self.env[model].browse(item[1] for item in items))
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2510, in delete
    records.unlink()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 557, in unlink
    return super(View, self).unlink()
  File "C:\odoo18_cubes\server\odoo\models.py", line 4491, in unlink
    self.modified(self._fields, before=True)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:47:08,994 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57227] 
2025-09-24 12:47:08,994 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1927] 
2025-09-24 12:47:08,997 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(513,) 
2025-09-24 12:47:08,998 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57226] 
2025-09-24 12:47:08,998 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [513] 
2025-09-24 12:47:08,998 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(668,) 
2025-09-24 12:47:09,035 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57225] 
2025-09-24 12:47:09,035 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [668] 
2025-09-24 12:47:09,044 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1926, 1925) 
2025-09-24 12:47:09,060 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57224, 57223] 
2025-09-24 12:47:09,060 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1926, 1925] 
2025-09-24 12:47:09,060 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(512,) 
2025-09-24 12:47:09,076 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57222] 
2025-09-24 12:47:09,076 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [512] 
2025-09-24 12:47:09,076 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(667,) 
2025-09-24 12:47:09,092 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57221] 
2025-09-24 12:47:09,092 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [667] 
2025-09-24 12:47:09,092 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1924,) 
2025-09-24 12:47:09,112 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57220] 
2025-09-24 12:47:09,112 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1924] 
2025-09-24 12:47:09,123 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.access(1035, 1034, 1033) 
2025-09-24 12:47:09,135 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57217, 57218, 57219] 
2025-09-24 12:47:09,135 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.access records with IDs: [1035, 1034, 1033] 
2025-09-24 12:47:09,137 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting res.groups(91,) 
2025-09-24 12:47:09,171 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57216] 
2025-09-24 12:47:09,171 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted res.groups records with IDs: [91] 
2025-09-24 12:47:09,252 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields(11206, 11205, 11204, 11203, 11202, 11201, 11200, 11199, 11194, 11192, 11191, 11190, 11189, 11184, 11182, 11181, 11180, 11179, 11178, 11177) 
2025-09-24 12:47:09,458 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57125, 57123, 57124, 57127, 57122, 57110, 57117, 57115, 57114, 57113, 57112, 57111, 57132, 57133, 57138, 57139, 57137, 57136, 57135, 57134] 
2025-09-24 12:47:09,458 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [11206, 11205, 11204, 11203, 11202, 11201, 11200, 11199, 11194, 11192, 11191, 11190, 11189, 11184, 11182, 11181, 11180, 11179, 11178, 11177] 
2025-09-24 12:47:09,468 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model(771, 770) 
2025-09-24 12:47:09,612 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57109, 57108] 
2025-09-24 12:47:09,612 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model records with IDs: [771, 770] 
2025-09-24 12:47:09,613 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-09-24 12:47:09,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [70961, 57227, 57226, 57225, 57224, 57223, 57222, 57221, 57220, 57219, 57218, 57217, 57216, 57139, 57138, 57137, 57136, 57135, 57134, 57133, 57132, 57131, 57130, 57129, 57128, 57127, 57126, 57125, 57124, 57123, 57122, 57121, 57120, 57119, 57118, 57117, 57116, 57115, 57114, 57113, 57112, 57111, 57110, 57109, 57108, 57107, 57106] 
2025-09-24 12:47:09,651 127508 INFO kayan_whatsapp odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-09-24 12:47:09,660 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:47:09,676 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-09-24 12:47:09,708 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:47:09,723 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:47:10,598 127508 INFO kayan_whatsapp odoo.modules.loading: loading 93 modules... 
2025-09-24 12:47:10,********** INFO kayan_whatsapp odoo.modules.loading: 93 modules loaded in 0.10s, 0 queries (+0 extra) 
2025-09-24 12:47:13,164 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:47:13,180 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:47:13,180 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 3.530s 
2025-09-24 12:47:13,205 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:47:13,205 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 6.943s 
2025-09-24 12:47:13,205 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 12:47:13,212 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:13] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 4567 3.333 3.649
2025-09-24 12:47:13,256 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:47:14,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:14] "GET /odoo HTTP/1.1" 200 - 94 0.106 1.533
2025-09-24 12:47:15,211 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:15] "GET /web/webclient/load_menus/6e78e4b96cdbe3a233e393fb3234f2a02e280faf49647929fe7dea5a68c6431e HTTP/1.1" 200 - 257 0.166 0.133
2025-09-24 12:47:15,222 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:15] "GET /web/webclient/translations/f1f898983c1373ec94686edb880d773f93273fb3?lang=en_US HTTP/1.1" 200 - 1 0.001 0.007
2025-09-24 12:47:15,458 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:15] "POST /web/action/load HTTP/1.1" 200 - 6 0.000 0.016
2025-09-24 12:47:15,655 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:15] "POST /mail/data HTTP/1.1" 200 - 69 0.055 0.053
2025-09-24 12:47:15,736 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:15] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.030 0.023
2025-09-24 12:47:15,736 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:15] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.029 0.025
2025-09-24 12:47:15,828 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:15] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.000 0.000
2025-09-24 12:47:15,907 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:15] "POST /mail/data HTTP/1.1" 200 - 65 0.109 0.116
2025-09-24 12:47:16,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:16] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.012
2025-09-24 12:47:16,311 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:16] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.016 0.000
2025-09-24 12:47:17,612 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:17] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.010
2025-09-24 12:47:43,785 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:43] "POST /web/action/load HTTP/1.1" 200 - 10 0.000 0.025
2025-09-24 12:47:44,161 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:44] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.074 0.063
2025-09-24 12:47:44,184 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.008
2025-09-24 12:47:44,582 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.069 0.035
2025-09-24 12:47:44,605 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:44] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.006 0.106
2025-09-24 12:47:48,361 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:48] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.047 0.051
2025-09-24 12:47:48,394 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:48] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.015 0.102
2025-09-24 12:47:49,662 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:49] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.064 0.043
2025-09-24 12:47:50,078 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:50] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.033 0.067
2025-09-24 12:47:53,660 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:53] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.052 0.028
2025-09-24 12:47:53,995 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:47:53] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.000 0.016
2025-09-24 12:48:05,849 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['Odoo 18 Recurring Payment'] to user admin #2 via 127.0.0.1 
2025-09-24 12:48:05,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:05] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 3 0.004 0.008
2025-09-24 12:48:06,193 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:06] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 15 0.010 0.019
2025-09-24 12:48:06,499 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:06] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 9 0.026 0.032
2025-09-24 12:48:08,062 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:08] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 11 0.016 0.018
2025-09-24 12:48:08,393 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['Odoo 18 Recurring Payment'] to user admin #2 via 127.0.0.1 
2025-09-24 12:48:08,393 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-09-24 12:48:08,395 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['Odoo 18 Recurring Payment'] to user admin #2 via 127.0.0.1 
2025-09-24 12:48:08,445 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:48:08,455 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-24 12:48:08,488 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:48:08,490 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:48:09,524 127508 INFO kayan_whatsapp odoo.modules.loading: loading 93 modules... 
2025-09-24 12:48:09,576 127508 INFO kayan_whatsapp odoo.modules.loading: 93 modules loaded in 0.05s, 0 queries (+0 extra) 
2025-09-24 12:48:10,574 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['Odoo 18 Recurring Payment'] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:48:10,590 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(516,) 
2025-09-24 12:48:10,649 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\wizard\base_module_uninstall.py", line 63, in action_uninstall
    return modules.button_immediate_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 633, in button_immediate_uninstall
    return self._button_immediate_function(self.env.registry[self._name].button_uninstall)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 558, in load_modules
    Module.browse(modules_to_remove.values()).module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\account\models\ir_module.py", line 109, in module_uninstall
    return super().module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\base_import_module\models\ir_module.py", line 327, in module_uninstall
    res = super().module_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 494, in module_uninstall
    self.env['ir.model.data']._module_data_uninstall(modules_to_remove)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2522, in _module_data_uninstall
    delete(self.env[model].browse(item[1] for item in items))
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2510, in delete
    records.unlink()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_menu.py", line 199, in unlink
    return super(IrUiMenu, self).unlink()
  File "C:\odoo18_cubes\server\odoo\models.py", line 4491, in unlink
    self.modified(self._fields, before=True)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:48:10,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57310] 
2025-09-24 12:48:10,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [516] 
2025-09-24 12:48:10,658 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(671,) 
2025-09-24 12:48:10,678 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57309] 
2025-09-24 12:48:10,678 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [671] 
2025-09-24 12:48:10,680 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1931, 1930) 
2025-09-24 12:48:10,697 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57308, 57307] 
2025-09-24 12:48:10,697 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1931, 1930] 
2025-09-24 12:48:10,********** INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(515, 514) 
2025-09-24 12:48:10,714 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57305, 57306] 
2025-09-24 12:48:10,714 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [515, 514] 
2025-09-24 12:48:10,715 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(670,) 
2025-09-24 12:48:10,730 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57304] 
2025-09-24 12:48:10,730 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [670] 
2025-09-24 12:48:10,730 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1929, 1928) 
2025-09-24 12:48:10,743 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57303, 57302] 
2025-09-24 12:48:10,744 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1929, 1928] 
2025-09-24 12:48:10,744 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.access(1038, 1037, 1036) 
2025-09-24 12:48:10,744 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57299, 57300, 57301] 
2025-09-24 12:48:10,744 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.access records with IDs: [1038, 1037, 1036] 
2025-09-24 12:48:10,744 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.cron(49,) 
2025-09-24 12:48:10,772 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57298] 
2025-09-24 12:48:10,772 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.cron records with IDs: [49] 
2025-09-24 12:48:10,774 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.server(669,) 
2025-09-24 12:48:10,790 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57297] 
2025-09-24 12:48:10,790 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.server records with IDs: [669] 
2025-09-24 12:48:10,790 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.sequence(30,) 
2025-09-24 12:48:10,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57296] 
2025-09-24 12:48:10,********** INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.sequence records with IDs: [30] 
2025-09-24 12:48:10,832 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields.selection(2640, 2639, 2638, 2637, 2636, 2635, 2634, 2633, 2632, 2631, 2630, 2629, 2628, 2627) 
2025-09-24 12:48:10,853 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57288, 57289, 57282, 57284, 57283, 57285, 57287, 57286, 57291, 57290, 57293, 57292, 57295, 57294] 
2025-09-24 12:48:10,853 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [2640, 2639, 2638, 2637, 2636, 2635, 2634, 2633, 2632, 2631, 2630, 2629, 2628, 2627] 
2025-09-24 12:48:10,853 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields(11253, 11251, 11250, 11249, 11248, 11247, 11246, 11245, 11244, 11243, 11238, 11236, 11235, 11234, 11233, 11232, 11231, 11230, 11229, 11228, 11227, 11226, 11225, 11224, 11223, 11222, 11221, 11216, 11214, 11213, 11212, 11211, 11210, 11209, 11208, 11207) 
2025-09-24 12:48:11,106 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57238, 57234, 57240, 57232, 57236, 57231, 57237, 57233, 57235, 57249, 57247, 57248, 57253, 57254, 57259, 57262, 57250, 57258, 57260, 57245, 57246, 57251, 57257, 57256, 57252, 57255, 57269, 57272, 57273, 57270, 57277, 57271, 57268, 57274, 57267, 57275] 
2025-09-24 12:48:11,106 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [11253, 11251, 11250, 11249, 11248, 11247, 11246, 11245, 11244, 11243, 11238, 11236, 11235, 11234, 11233, 11232, 11231, 11230, 11229, 11228, 11227, 11226, 11225, 11224, 11223, 11222, 11221, 11216, 11214, 11213, 11212, 11211, 11210, 11209, 11208, 11207] 
2025-09-24 12:48:11,107 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model(774, 773, 772) 
2025-09-24 12:48:11,191 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57228, 57229, 57230] 
2025-09-24 12:48:11,191 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model records with IDs: [774, 773, 772] 
2025-09-24 12:48:11,192 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-09-24 12:48:11,200 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [57310, 57309, 57308, 57307, 57306, 57305, 57304, 57303, 57302, 57301, 57300, 57299, 57298, 57297, 57296, 57295, 57294, 57293, 57292, 57291, 57290, 57289, 57288, 57287, 57286, 57285, 57284, 57283, 57282, 57281, 57280, 57279, 57278, 57277, 57276, 57275, 57274, 57273, 57272, 57271, 57270, 57269, 57268, 57267, 57266, 57265, 57264, 57263, 57262, 57261, 57260, 57259, 57258, 57257, 57256, 57255, 57254, 57253, 57252, 57251, 57250, 57249, 57248, 57247, 57246, 57245, 57244, 57243, 57242, 57241, 57240, 57239, 57238, 57237, 57236, 57235, 57234, 57233, 57232, 57231, 57230, 57229, 57228] 
2025-09-24 12:48:11,224 127508 INFO kayan_whatsapp odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-09-24 12:48:11,235 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:48:11,245 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-24 12:48:11,262 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:48:11,262 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:48:12,334 127508 INFO kayan_whatsapp odoo.modules.loading: loading 92 modules... 
2025-09-24 12:48:12,438 127508 INFO kayan_whatsapp odoo.modules.loading: 92 modules loaded in 0.10s, 0 queries (+0 extra) 
2025-09-24 12:48:14,424 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:48:14,453 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:48:14,453 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 3.229s 
2025-09-24 12:48:14,453 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:48:14,453 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 6.031s 
2025-09-24 12:48:14,453 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 12:48:14,474 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:14] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 4619 2.833 3.264
2025-09-24 12:48:14,500 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:48:16,218 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:16] "GET /odoo HTTP/1.1" 200 - 97 0.084 1.634
2025-09-24 12:48:16,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:16] "GET /web/webclient/load_menus/56b98707fb4be505b15c02ead649be697fc23dd416fbbd66025dc2181a65574a HTTP/1.1" 200 - 255 0.146 0.140
2025-09-24 12:48:16,595 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:16] "GET /web/webclient/translations/86796d4d7efd2fa44d82557c4ddeb6f9b7eaa764?lang=en_US HTTP/1.1" 200 - 1 0.000 0.033
2025-09-24 12:48:16,935 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:16] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.016 0.004
2025-09-24 12:48:17,199 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:17] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.000 0.016
2025-09-24 12:48:17,515 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:17] "POST /mail/data HTTP/1.1" 200 - 69 0.029 0.115
2025-09-24 12:48:17,546 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:17] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.005 0.000
2025-09-24 12:48:18,801 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:18] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.000
2025-09-24 12:48:24,830 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:24] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.013 0.019
2025-09-24 12:48:25,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:25] "POST /web/action/load HTTP/1.1" 200 - 6 0.000 0.015
2025-09-24 12:48:25,639 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:25] "POST /mail/data HTTP/1.1" 200 - 65 0.044 0.051
2025-09-24 12:48:25,795 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:25] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.000 0.000
2025-09-24 12:48:28,753 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:28] "POST /web/action/load HTTP/1.1" 200 - 10 0.008 0.014
2025-09-24 12:48:29,222 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:29] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.068 0.075
2025-09-24 12:48:29,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:29] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.006
2025-09-24 12:48:29,651 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:29] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.069 0.052
2025-09-24 12:48:29,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:29] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.022 0.105
2025-09-24 12:48:31,725 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:31] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.043 0.036
2025-09-24 12:48:32,035 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:32] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.006 0.075
2025-09-24 12:48:32,630 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:32] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.051 0.046
2025-09-24 12:48:32,817 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:32] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.013 0.006
2025-09-24 12:48:35,249 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 12:48:35,249 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 12:48:35,261 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 12:48:35,544 127508 WARNING kayan_whatsapp odoo.http: Unable to install module "base_accounting_kit" because an external dependency is not met: External dependency qifparse not installed: No package metadata was found for qifparse 
2025-09-24 12:48:35,544 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:48:35] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 26 0.012 0.290
2025-09-24 12:49:09,882 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:09] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.072 0.032
2025-09-24 12:49:09,914 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:09] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.000 0.131
2025-09-24 12:49:10,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:10] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.096 0.020
2025-09-24 12:49:10,480 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:10] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.000 0.007
2025-09-24 12:49:11,900 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Budget Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:49:11,900 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 12:49:11,900 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Budget Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:49:12,047 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:49:12,048 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-24 12:49:12,096 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:49:12,096 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:49:13,452 127508 INFO kayan_whatsapp odoo.modules.loading: loading 92 modules... 
2025-09-24 12:49:13,548 127508 INFO kayan_whatsapp odoo.modules.loading: 92 modules loaded in 0.10s, 0 queries (+0 extra) 
2025-09-24 12:49:13,548 127508 INFO kayan_whatsapp odoo.modules.loading: loading 93 modules... 
2025-09-24 12:49:13,548 127508 INFO kayan_whatsapp odoo.modules.loading: Loading module base_account_budget (59/93) 
2025-09-24 12:49:13,564 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\api.py:466: DeprecationWarning: The model odoo.addons.base_account_budget.models.account_budget is not overriding the create method in batch
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 480, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18_cubes\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "c:\odoo18_cubes\server\custom_addons\base_account_budget\__init__.py", line 22, in <module>
    from . import models
  File "c:\odoo18_cubes\server\custom_addons\base_account_budget\models\__init__.py", line 22, in <module>
    from . import account_budget
  File "c:\odoo18_cubes\server\custom_addons\base_account_budget\models\account_budget.py", line 26, in <module>
    class AccountBudgetPost(models.Model):
  File "c:\odoo18_cubes\server\custom_addons\base_account_budget\models\account_budget.py", line 52, in AccountBudgetPost
    @api.model
  File "C:\odoo18_cubes\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\odoo18_cubes\server\odoo\api.py", line 466, in model_create_single
    warnings.warn(
 
2025-09-24 12:49:13,937 127508 INFO kayan_whatsapp odoo.modules.registry: module base_account_budget: creating or updating database tables 
2025-09-24 12:49:14,257 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 480, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 205, in load_module_graph
    registry.init_models(env.cr, model_names, {'module': package.name}, new_install)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 614, in init_models
    func()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 59, in mark_modified
    records.modified(fnames)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:49:14,616 127508 INFO kayan_whatsapp odoo.modules.loading: loading base_account_budget/security/account_budget_security.xml 
2025-09-24 12:49:14,691 127508 INFO kayan_whatsapp odoo.modules.loading: loading base_account_budget/security/ir.model.access.csv 
2025-09-24 12:49:14,705 127508 INFO kayan_whatsapp odoo.modules.loading: loading base_account_budget/views/account_analytic_account_views.xml 
2025-09-24 12:49:14,733 127508 INFO kayan_whatsapp odoo.modules.loading: loading base_account_budget/views/account_budget_views.xml 
2025-09-24 12:49:14,811 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-09-24 12:49:14,812 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: <img> tag must contain an alt attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\base_account_budget\\views\\account_budget_views.xml',
 'line': 30,
 'name': 'budget.kanban',
 'view': ir.ui.view(1979,),
 'view.model': 'budget.budget',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_budget_kanban'} 
2025-09-24 12:49:14,857 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module base_account_budget: no translation for language ar_001 
2025-09-24 12:49:14,974 127508 INFO kayan_whatsapp odoo.modules.loading: Module base_account_budget loaded in 1.43s, 391 queries (+391 other) 
2025-09-24 12:49:14,974 127508 INFO kayan_whatsapp odoo.modules.loading: 93 modules loaded in 1.43s, 391 queries (+391 extra) 
2025-09-24 12:49:17,310 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:49:17,329 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:49:17,330 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 5.301s 
2025-09-24 12:49:17,330 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 12:49:17,336 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:17] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 3565 3.118 2.334
2025-09-24 12:49:17,665 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:49:19,239 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:19] "GET /odoo HTTP/1.1" 200 - 97 0.104 1.470
2025-09-24 12:49:19,293 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:19] "GET /web/webclient/translations/fb63028a6afcfba4d55e88d5ea3c3fabba1f73c7?lang=en_US HTTP/1.1" 200 - 1 0.002 0.019
2025-09-24 12:49:19,687 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:19] "GET /web/webclient/load_menus/fdb726741bec2baf08b21440dfb5ed865f05f3b5d685e3fd8f27101a1e3b9223 HTTP/1.1" 200 - 258 0.208 0.207
2025-09-24 12:49:19,942 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:19] "POST /web/action/load HTTP/1.1" 200 - 6 0.003 0.045
2025-09-24 12:49:19,962 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:19] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.040 0.027
2025-09-24 12:49:20,129 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:20] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.043 0.043
2025-09-24 12:49:20,147 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:20] "POST /mail/data HTTP/1.1" 200 - 69 0.159 0.107
2025-09-24 12:49:20,165 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:20] "GET /web/image/res.partner/3/avatar_128?unique=1758710952000 HTTP/1.1" 200 - 9 0.058 0.057
2025-09-24 12:49:20,210 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:20] "POST /mail/data HTTP/1.1" 200 - 65 0.083 0.088
2025-09-24 12:49:20,329 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:20] "POST /mail/inbox/messages HTTP/1.1" 200 - 4 0.004 0.017
2025-09-24 12:49:20,640 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:20] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.006 0.019
2025-09-24 12:49:20,670 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:20] "GET /web/image/res.partner/2/avatar_128?unique=1758718152000 HTTP/1.1" 200 - 13 0.022 0.035
2025-09-24 12:49:20,988 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:20] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.001 0.008
2025-09-24 12:49:22,207 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:22] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.002 0.011
2025-09-24 12:49:22,940 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:22] "POST /web/action/load HTTP/1.1" 200 - 10 0.016 0.010
2025-09-24 12:49:23,446 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:23] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.053 0.094
2025-09-24 12:49:23,556 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:23] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.008
2025-09-24 12:49:23,861 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:23] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.031 0.049
2025-09-24 12:49:23,888 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:23] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.011 0.096
2025-09-24 12:49:24,846 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:24] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.046 0.036
2025-09-24 12:49:25,148 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:25] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.015 0.055
2025-09-24 12:49:27,994 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:27] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.036 0.031
2025-09-24 12:49:28,195 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:28] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.013 0.000
2025-09-24 12:49:30,205 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 12:49:30,205 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 12:49:30,207 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 12:49:30,282 127508 WARNING kayan_whatsapp odoo.http: Unable to install module "base_accounting_kit" because an external dependency is not met: External dependency qifparse not installed: No package metadata was found for qifparse 
2025-09-24 12:49:30,282 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:30] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 24 0.033 0.058
2025-09-24 12:49:35,765 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:35] "POST /web/action/load HTTP/1.1" 200 - 10 0.015 0.005
2025-09-24 12:49:36,146 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:36] "POST /web/dataset/call_kw/account.journal/get_views HTTP/1.1" 200 - 50 0.040 0.097
2025-09-24 12:49:36,273 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:36] "POST /web/dataset/call_kw/account.journal/web_search_read HTTP/1.1" 200 - 38 0.059 0.052
2025-09-24 12:49:36,612 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:36] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.000
2025-09-24 12:49:36,633 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:36] "GET /web/bundle/web.chartjs_lib?lang=en_US&debug=1 HTTP/1.1" 200 - 2 0.003 0.005
2025-09-24 12:49:41,391 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:41] "POST /web/action/load HTTP/1.1" 200 - 9 0.012 0.020
2025-09-24 12:49:42,384 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:42] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 391 0.341 0.313
2025-09-24 12:49:42,628 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:42] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 203 0.108 0.119
2025-09-24 12:49:43,061 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:43] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.016 0.000
2025-09-24 12:49:43,065 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:43] "POST /base_setup/data HTTP/1.1" 200 - 6 0.020 0.000
2025-09-24 12:49:44,073 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.002 0.009
2025-09-24 12:49:44,485 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.081 0.024
2025-09-24 12:49:44,502 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:44] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.007 0.115
2025-09-24 12:49:47,428 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:47] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.047 0.053
2025-09-24 12:49:47,475 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:47] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.024 0.122
2025-09-24 12:49:47,996 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:47] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.118 0.050
2025-09-24 12:49:48,339 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:48] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.016 0.011
2025-09-24 12:49:51,208 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall_wizard on ['Odoo 18 Budget Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:49:51,208 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:51] "POST /web/dataset/call_button/ir.module.module/button_uninstall_wizard HTTP/1.1" 200 - 3 0.000 0.008
2025-09-24 12:49:51,581 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:51] "POST /web/dataset/call_kw/base.module.uninstall/get_views HTTP/1.1" 200 - 15 0.010 0.025
2025-09-24 12:49:51,842 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:51] "POST /web/dataset/call_kw/base.module.uninstall/onchange HTTP/1.1" 200 - 11 0.007 0.024
2025-09-24 12:49:53,780 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:53] "POST /web/dataset/call_kw/base.module.uninstall/web_save HTTP/1.1" 200 - 12 0.019 0.027
2025-09-24 12:49:54,112 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_uninstall on ['Odoo 18 Budget Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:49:54,112 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module uninstallation 
2025-09-24 12:49:54,112 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_uninstall on ['Odoo 18 Budget Management'] to user admin #2 via 127.0.0.1 
2025-09-24 12:49:54,161 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:49:54,172 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-24 12:49:54,195 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:49:54,211 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:49:55,080 127508 INFO kayan_whatsapp odoo.modules.loading: loading 93 modules... 
2025-09-24 12:49:55,177 127508 INFO kayan_whatsapp odoo.modules.loading: 93 modules loaded in 0.09s, 0 queries (+0 extra) 
2025-09-24 12:49:56,081 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.module_uninstall on ['Odoo 18 Budget Management'] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:49:56,091 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(563,) 
2025-09-24 12:49:56,********** WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\wizard\base_module_uninstall.py", line 63, in action_uninstall
    return modules.button_immediate_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 633, in button_immediate_uninstall
    return self._button_immediate_function(self.env.registry[self._name].button_uninstall)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 558, in load_modules
    Module.browse(modules_to_remove.values()).module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\account\models\ir_module.py", line 109, in module_uninstall
    return super().module_uninstall()
  File "C:\odoo18_cubes\server\odoo\addons\base_import_module\models\ir_module.py", line 327, in module_uninstall
    res = super().module_uninstall()
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 494, in module_uninstall
    self.env['ir.model.data']._module_data_uninstall(modules_to_remove)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2522, in _module_data_uninstall
    delete(self.env[model].browse(item[1] for item in items))
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 2510, in delete
    records.unlink()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_menu.py", line 199, in unlink
    return super(IrUiMenu, self).unlink()
  File "C:\odoo18_cubes\server\odoo\models.py", line 4491, in unlink
    self.modified(self._fields, before=True)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 12:49:56,147 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82567] 
2025-09-24 12:49:56,147 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [563] 
2025-09-24 12:49:56,147 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(700,) 
2025-09-24 12:49:56,179 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82566] 
2025-09-24 12:49:56,179 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [700] 
2025-09-24 12:49:56,179 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1982, 1981, 1980) 
2025-09-24 12:49:56,194 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82565, 82563, 82564] 
2025-09-24 12:49:56,194 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1982, 1981, 1980] 
2025-09-24 12:49:56,194 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(562,) 
2025-09-24 12:49:56,210 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82562] 
2025-09-24 12:49:56,210 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [562] 
2025-09-24 12:49:56,214 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(699,) 
2025-09-24 12:49:56,226 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82561] 
2025-09-24 12:49:56,226 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [699] 
2025-09-24 12:49:56,226 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1979, 1978, 1977, 1976) 
2025-09-24 12:49:56,241 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82559, 82558, 82560, 82557] 
2025-09-24 12:49:56,241 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1979, 1978, 1977, 1976] 
2025-09-24 12:49:56,241 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.menu(561,) 
2025-09-24 12:49:56,263 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82556] 
2025-09-24 12:49:56,263 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.menu records with IDs: [561] 
2025-09-24 12:49:56,263 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.actions.act_window(698,) 
2025-09-24 12:49:56,288 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82555] 
2025-09-24 12:49:56,288 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window records with IDs: [698] 
2025-09-24 12:49:56,289 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.ui.view(1975, 1974, 1973) 
2025-09-24 12:49:56,304 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82552, 82553, 82554] 
2025-09-24 12:49:56,304 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [1975, 1974, 1973] 
2025-09-24 12:49:56,304 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.access(1154, 1153, 1152, 1151, 1150, 1149) 
2025-09-24 12:49:56,320 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82547, 82548, 82551, 82546, 82549, 82550] 
2025-09-24 12:49:56,322 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.access records with IDs: [1154, 1153, 1152, 1151, 1150, 1149] 
2025-09-24 12:49:56,323 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.rule(272, 271, 270) 
2025-09-24 12:49:56,336 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82543, 82544, 82542] 
2025-09-24 12:49:56,336 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.rule records with IDs: [272, 271, 270] 
2025-09-24 12:49:56,336 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields.selection(2993, 2992, 2991, 2990, 2989) 
2025-09-24 12:49:56,353 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82524, 82525, 82527, 82523, 82526] 
2025-09-24 12:49:56,353 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [2993, 2992, 2991, 2990, 2989] 
2025-09-24 12:49:56,********** INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model.fields(12374, 12369, 12367, 12366, 12365, 12364, 12363, 12362, 12361, 12360, 12359, 12358, 12357, 12352, 12350, 12349, 12348, 12347, 12346, 12345, 12344, 12343, 12342, 12341, 12340, 12339, 12338, 12337, 12336, 12335, 12334, 12333, 12332, 12331, 12326, 12324, 12323, 12322, 12321) 
2025-09-24 12:49:56,717 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82522, 82470, 82471, 82472, 82474, 82469, 82497, 82498, 82493, 82494, 82495, 82500, 82483, 82488, 82480, 82486, 82487, 82491, 82482, 82479, 82484, 82485, 82481, 82492, 82489, 82496, 82490, 82506, 82505, 82515, 82508, 82509, 82517, 82507, 82510, 82514, 82511, 82512, 82513] 
2025-09-24 12:49:56,717 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [12374, 12369, 12367, 12366, 12365, 12364, 12363, 12362, 12361, 12360, 12359, 12358, 12357, 12352, 12350, 12349, 12348, 12347, 12346, 12345, 12344, 12343, 12342, 12341, 12340, 12339, 12338, 12337, 12336, 12335, 12334, 12333, 12332, 12331, 12326, 12324, 12323, 12322, 12321] 
2025-09-24 12:49:56,724 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.relation records with IDs: [216] 
2025-09-24 12:49:56,736 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Dropped table account_budget_rel 
2025-09-24 12:49:56,739 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: Deleting ir.model(818, 817, 816) 
2025-09-24 12:49:56,852 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82466, 82467, 82468] 
2025-09-24 12:49:56,852 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model records with IDs: [818, 817, 816] 
2025-09-24 12:49:56,852 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_model: ir.model.data could not be deleted ([]) 
2025-09-24 12:49:56,870 127508 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [82567, 82566, 82565, 82564, 82563, 82562, 82561, 82560, 82559, 82558, 82557, 82556, 82555, 82554, 82553, 82552, 82551, 82550, 82549, 82548, 82547, 82546, 82544, 82543, 82542, 82534, 82527, 82526, 82525, 82524, 82523, 82522, 82521, 82520, 82519, 82518, 82517, 82516, 82515, 82514, 82513, 82512, 82511, 82510, 82509, 82508, 82507, 82506, 82505, 82504, 82503, 82502, 82501, 82500, 82499, 82498, 82497, 82496, 82495, 82494, 82493, 82492, 82491, 82490, 82489, 82488, 82487, 82486, 82485, 82484, 82483, 82482, 82481, 82480, 82479, 82478, 82477, 82476, 82475, 82474, 82473, 82472, 82471, 82470, 82469, 82468, 82467, 82466, 82465] 
2025-09-24 12:49:56,901 127508 INFO kayan_whatsapp odoo.modules.loading: Reloading registry once more after uninstalling modules 
2025-09-24 12:49:56,916 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 12:49:56,926 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-24 12:49:56,958 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 12:49:56,958 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 12:49:58,355 127508 INFO kayan_whatsapp odoo.modules.loading: loading 92 modules... 
2025-09-24 12:49:58,451 127508 INFO kayan_whatsapp odoo.modules.loading: 92 modules loaded in 0.10s, 0 queries (+0 extra) 
2025-09-24 12:49:59,864 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 12:49:59,864 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:49:59,864 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 2.963s 
2025-09-24 12:49:59,887 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 12:49:59,887 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 5.744s 
2025-09-24 12:49:59,887 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 12:49:59,896 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:49:59] "POST /web/dataset/call_button/base.module.uninstall/action_uninstall HTTP/1.1" 200 - 4615 2.912 2.872
2025-09-24 12:49:59,939 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 12:50:01,762 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:01] "GET /odoo HTTP/1.1" 200 - 97 0.081 1.746
2025-09-24 12:50:01,932 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:01] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.008 0.022
2025-09-24 12:50:02,363 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:02] "POST /web/action/load HTTP/1.1" 200 - 7 0.023 0.028
2025-09-24 12:50:02,382 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:02] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.035 0.031
2025-09-24 12:50:02,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:02] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.006 0.045
2025-09-24 12:50:02,547 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:02] "POST /mail/data HTTP/1.1" 200 - 69 0.127 0.103
2025-09-24 12:50:02,736 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:02] "POST /mail/data HTTP/1.1" 200 - 65 0.070 0.095
2025-09-24 12:50:02,785 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:02] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.012 0.014
2025-09-24 12:50:02,850 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:02] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.002 0.038
2025-09-24 12:50:04,589 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:04] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.007
2025-09-24 12:50:06,546 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:06] "GET /odoo/discuss HTTP/1.1" 200 - 15 0.007 0.048
2025-09-24 12:50:06,817 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:06] "POST /web/action/load HTTP/1.1" 200 - 7 0.007 0.013
2025-09-24 12:50:07,063 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:07] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.025 0.017
2025-09-24 12:50:07,180 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:07] "POST /mail/data HTTP/1.1" 200 - 38 0.068 0.096
2025-09-24 12:50:07,180 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:07] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.000 0.012
2025-09-24 12:50:07,198 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:07] "POST /mail/data HTTP/1.1" 200 - 59 0.110 0.069
2025-09-24 12:50:07,416 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:07] "POST /mail/inbox/messages HTTP/1.1" 200 - 3 0.003 0.014
2025-09-24 12:50:07,563 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:07] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 3 0.008 0.001
2025-09-24 12:50:07,905 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:07] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.016
2025-09-24 12:50:09,134 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:09] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.015
2025-09-24 12:50:13,885 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:13] "GET /web/session/logout HTTP/1.1" 303 - 3 0.006 0.020
2025-09-24 12:50:14,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:14] "GET /odoo HTTP/1.1" 303 - 1 0.001 0.014
2025-09-24 12:50:14,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:14] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 45 0.018 0.311
2025-09-24 12:50:15,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:15] "GET /web/binary/company_logo HTTP/1.1" 200 - 2 0.011 0.006
2025-09-24 12:50:21,********** INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/668d8d1/web.assets_frontend.min.css (id:1147) 
2025-09-24 12:50:21,********** INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Deleting attachments [194] (matching /web/assets/_______/web.assets_frontend.min.css) because it was replaced with /web/assets/668d8d1/web.assets_frontend.min.css 
2025-09-24 12:50:21,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:21] "GET /web/assets/aaef59a/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 4 0.012 0.046
2025-09-24 12:50:21,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:21] "GET /web/assets/668d8d1/web.assets_frontend.min.css HTTP/1.1" 200 - 14 0.044 6.679
2025-09-24 12:50:40,********** INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1aa56b0/web.assets_frontend_lazy.min.js (id:1148) 
2025-09-24 12:50:40,********** INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Deleting attachments [195] (matching /web/assets/_______/web.assets_frontend_lazy.min.js) because it was replaced with /web/assets/1aa56b0/web.assets_frontend_lazy.min.js 
2025-09-24 12:50:40,975 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:40] "GET /web/assets/1aa56b0/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 11 0.019 19.043
2025-09-24 12:50:41,210 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:50:41] "GET /website/translations/d0a9022c4cf5b641e58121732ed8c08a6d2829a6 HTTP/1.1" 200 - 5 0.004 0.015
2025-09-24 12:51:53,408 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:51:53,420 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.000s 
2025-09-24 12:51:53,423 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:51:53,423 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:54:04,761 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 12:54:04,769 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.009s 
2025-09-24 12:54:04,773 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 12:54:04,780 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 12:56:53,496 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:56:53,504 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.008s 
2025-09-24 12:56:53,514 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:56:53,524 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:01:53,599 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 13:01:53,********** INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.009s 
2025-09-24 13:01:53,613 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 13:01:53,********** INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:04:15,000 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 13:04:15,000 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.000s 
2025-09-24 13:04:15,011 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 13:04:15,017 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 13:06:17,083 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 13:06:17,095 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.010s 
2025-09-24 13:06:17,104 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 13:06:17,116 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:11:22,245 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 13:11:22,255 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.009s 
2025-09-24 13:11:22,255 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 13:11:22,285 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:14:25,360 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 13:14:25,377 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.017s 
2025-09-24 13:14:25,377 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 13:14:25,392 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 13:16:27,449 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 13:16:27,464 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.015s 
2025-09-24 13:16:27,464 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 13:16:27,464 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:18:29,556 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) starting 
2025-09-24 13:18:30,012 127508 INFO kayan_whatsapp odoo.addons.whatsapp_evolution.models.whatsapp_stock_monitor: No low stock products found 
2025-09-24 13:18:30,012 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) done in 0.456s 
2025-09-24 13:18:30,012 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) processed 0 records, 0 records remaining 
2025-09-24 13:18:30,030 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) completed 
2025-09-24 13:21:33,136 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 13:21:33,143 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.007s 
2025-09-24 13:21:33,153 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 13:21:33,160 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:22:48,********** INFO kayan_whatsapp odoo.addons.base.models.res_users: Login failed for db:kayan_whatsapp login:admin from 127.0.0.1 
2025-09-24 13:22:48,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:22:48] "POST /web/login HTTP/1.1" 200 - 8 0.016 0.923
2025-09-24 13:22:48,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:22:48] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.009 0.011
2025-09-24 13:22:58,********** INFO kayan_whatsapp odoo.addons.base.models.res_users: Login failed for db:kayan_whatsapp login:admin from 127.0.0.1 
2025-09-24 13:22:58,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:22:58] "POST /web/login HTTP/1.1" 200 - 5 0.008 1.018
2025-09-24 13:22:58,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:22:58] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.002 0.016
2025-09-24 13:23:05,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/database/manager HTTP/1.1" 200 - 3 0.007 0.394
2025-09-24 13:23:05,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/static/src/libs/fontawesome/css/font-awesome.css HTTP/1.1" 200 - 0 0.000 0.009
2025-09-24 13:23:05,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/static/lib/bootstrap/dist/css/bootstrap.css HTTP/1.1" 200 - 0 0.000 0.022
2025-09-24 13:23:05,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/static/lib/bootstrap/js/dist/dom/data.js HTTP/1.1" 200 - 0 0.000 0.031
2025-09-24 13:23:05,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/static/lib/bootstrap/js/dist/dom/event-handler.js HTTP/1.1" 200 - 0 0.000 0.031
2025-09-24 13:23:05,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/static/lib/bootstrap/js/dist/util/index.js HTTP/1.1" 200 - 0 0.000 0.038
2025-09-24 13:23:05,719 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/static/lib/bootstrap/js/dist/dom/manipulator.js HTTP/1.1" 200 - 0 0.000 0.033
2025-09-24 13:23:05,725 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/static/lib/bootstrap/js/dist/dom/selector-engine.js HTTP/1.1" 200 - 0 0.000 0.037
2025-09-24 13:23:05,733 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:05] "GET /web/static/lib/bootstrap/js/dist/util/config.js HTTP/1.1" 200 - 0 0.000 0.024
2025-09-24 13:23:06,033 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:06] "GET /web/static/lib/bootstrap/js/dist/util/component-functions.js HTTP/1.1" 200 - 0 0.000 0.014
2025-09-24 13:23:06,033 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:06] "GET /web/static/lib/bootstrap/js/dist/util/backdrop.js HTTP/1.1" 200 - 0 0.000 0.005
2025-09-24 13:23:06,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:06] "GET /web/static/lib/bootstrap/js/dist/util/focustrap.js HTTP/1.1" 200 - 0 0.000 0.012
2025-09-24 13:23:06,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:06] "GET /web/static/lib/bootstrap/js/dist/base-component.js HTTP/1.1" 200 - 0 0.000 0.004
2025-09-24 13:23:06,066 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:06] "GET /web/static/lib/bootstrap/js/dist/util/scrollbar.js HTTP/1.1" 200 - 0 0.000 0.027
2025-09-24 13:23:06,072 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:06] "GET /web/static/lib/bootstrap/js/dist/modal.js HTTP/1.1" 200 - 0 0.000 0.023
2025-09-24 13:23:06,369 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:06] "GET /web/static/img/logo2.png HTTP/1.1" 200 - 0 0.000 0.012
2025-09-24 13:23:06,429 127508 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:06] "GET /web/static/src/public/database_manager.js HTTP/1.1" 200 - 0 0.000 0.075
2025-09-24 13:23:07,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:07] "GET /odoo?db=kayan_whatsapp HTTP/1.1" 303 - 1 0.000 0.015
2025-09-24 13:23:08,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:08] "GET /web/login?redirect=/odoo?db%3Dkayan_whatsapp HTTP/1.1" 200 - 3 0.002 0.063
2025-09-24 13:23:08,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:08] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.003 0.008
2025-09-24 13:23:21,********** INFO kayan_whatsapp odoo.addons.base.models.res_users: Login successful for db:kayan_whatsapp login:admin from 127.0.0.1 
2025-09-24 13:23:21,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:21] "POST /web/login HTTP/1.1" 303 - 20 0.047 1.194
2025-09-24 13:23:21,********** INFO kayan_whatsapp odoo.addons.base.models.res_device: User 2 inserts device log (O6xTLBj5MeJANAN3rJFK0XJKzF77FLlVY3_y__c4t2) 
2025-09-24 13:23:22,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:22] "GET /odoo?db=kayan_whatsapp HTTP/1.1" 200 - 16 0.060 0.091
2025-09-24 13:23:22,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:22] "POST /web/action/load HTTP/1.1" 200 - 6 0.003 0.051
2025-09-24 13:23:22,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:22] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 3 0.023 0.038
2025-09-24 13:23:22,715 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:22] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.026 0.059
2025-09-24 13:23:23,296 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:23] "POST /mail/data HTTP/1.1" 200 - 38 0.313 0.360
2025-09-24 13:23:23,315 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:23] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.058 0.252
2025-09-24 13:23:23,416 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:23] "POST /mail/data HTTP/1.1" 200 - 59 0.468 0.103
2025-09-24 13:23:23,462 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:23] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.001 0.009
2025-09-24 13:23:23,484 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:23] "POST /mail/inbox/messages HTTP/1.1" 200 - 3 0.000 0.011
2025-09-24 13:23:28,615 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:28] "POST /web/action/load HTTP/1.1" 200 - 10 0.010 0.006
2025-09-24 13:23:28,982 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:28] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.054 0.082
2025-09-24 13:23:29,004 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:29] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.011 0.008
2025-09-24 13:23:29,416 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:29] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.065 0.049
2025-09-24 13:23:29,483 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:29] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.023 0.158
2025-09-24 13:23:32,385 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Accounting Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:23:32,385 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 13:23:32,385 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Accounting Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:23:32,599 127508 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 13:23:32,634 127508 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.03s, 0 queries (+0 extra) 
2025-09-24 13:23:32,684 127508 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 13:23:32,684 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-24 13:23:34,037 127508 INFO kayan_whatsapp odoo.modules.loading: loading 92 modules... 
2025-09-24 13:23:34,230 127508 INFO kayan_whatsapp odoo.modules.loading: 92 modules loaded in 0.19s, 0 queries (+0 extra) 
2025-09-24 13:23:34,230 127508 INFO kayan_whatsapp odoo.modules.loading: loading 98 modules... 
2025-09-24 13:23:34,230 127508 INFO kayan_whatsapp odoo.modules.loading: Loading module om_account_asset (65/98) 
2025-09-24 13:23:34,716 127508 INFO kayan_whatsapp odoo.modules.registry: module om_account_asset: creating or updating database tables 
2025-09-24 13:23:34,939 127508 INFO kayan_whatsapp odoo.models: Prepare computation of account.move.line.asset_start_date 
2025-09-24 13:23:34,939 127508 INFO kayan_whatsapp odoo.models: Prepare computation of account.move.line.asset_end_date 
2025-09-24 13:23:34,939 127508 INFO kayan_whatsapp odoo.models: Prepare computation of account.move.line.asset_mrr 
2025-09-24 13:23:35,360 127508 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1892, in _serve_db
    return self._transactioning(
  File "C:\odoo18_cubes\server\odoo\http.py", line 1955, in _transactioning
    return service_model.retrying(func, env=self.env)
  File "C:\odoo18_cubes\server\odoo\service\model.py", line 137, in retrying
    result = func()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1922, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2169, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_http.py", line 329, in _dispatch
    result = endpoint(**request.params)
  File "C:\odoo18_cubes\server\odoo\http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\dataset.py", line 40, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
  File "C:\odoo18_cubes\server\odoo\api.py", line 517, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 480, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 205, in load_module_graph
    registry.init_models(env.cr, model_names, {'module': package.name}, new_install)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 614, in init_models
    func()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_model.py", line 59, in mark_modified
    records.modified(fnames)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 13:23:35,799 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/data/account_asset_data.xml 
2025-09-24 13:23:35,863 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/security/account_asset_security.xml 
2025-09-24 13:23:35,914 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/security/ir.model.access.csv 
2025-09-24 13:23:35,974 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/wizard/asset_depreciation_confirmation_wizard_views.xml 
2025-09-24 13:23:36,056 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/wizard/asset_modify_views.xml 
2025-09-24 13:23:36,095 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/views/account_asset_views.xml 
2025-09-24 13:23:36,150 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-09-24 13:23:36,193 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/views/account_move_views.xml 
2025-09-24 13:23:36,260 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/views/account_asset_templates.xml 
2025-09-24 13:23:36,295 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/views/asset_category_views.xml 
2025-09-24 13:23:36,339 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-09-24 13:23:36,370 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/views/product_views.xml 
2025-09-24 13:23:36,424 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_asset/report/account_asset_report_views.xml 
2025-09-24 13:23:36,561 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module om_account_asset: loading translation file c:\odoo18_cubes\server\custom_addons\om_account_asset\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:36,577 127508 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\om_account_asset\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:36,672 127508 INFO kayan_whatsapp odoo.modules.loading: Module om_account_asset loaded in 2.44s, 546 queries (+546 other) 
2025-09-24 13:23:36,672 127508 INFO kayan_whatsapp odoo.modules.loading: Loading module om_account_budget (66/98) 
2025-09-24 13:23:36,943 127508 INFO kayan_whatsapp odoo.modules.registry: module om_account_budget: creating or updating database tables 
2025-09-24 13:23:37,836 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_budget/security/ir.model.access.csv 
2025-09-24 13:23:37,870 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_budget/security/security.xml 
2025-09-24 13:23:37,966 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_budget/views/account_analytic_account_views.xml 
2025-09-24 13:23:38,010 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_budget/views/account_budget_views.xml 
2025-09-24 13:23:38,080 127508 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-09-24 13:23:38,175 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_budget/views/res_config_settings_views.xml 
2025-09-24 13:23:38,301 127508 INFO kayan_whatsapp odoo.modules.loading: Module om_account_budget: loading demo 
2025-09-24 13:23:38,301 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_budget/data/account_budget_demo.xml 
2025-09-24 13:23:38,541 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module om_account_budget: loading translation file c:\odoo18_cubes\server\custom_addons\om_account_budget\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:38,541 127508 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\om_account_budget\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:38,********** INFO kayan_whatsapp odoo.modules.loading: Module om_account_budget loaded in 1.94s, 560 queries (+560 other) 
2025-09-24 13:23:38,********** INFO kayan_whatsapp odoo.modules.loading: Loading module om_fiscal_year (68/98) 
2025-09-24 13:23:38,********** INFO kayan_whatsapp odoo.modules.registry: module om_fiscal_year: creating or updating database tables 
2025-09-24 13:23:38,********** WARNING kayan_whatsapp odoo.addons.base.models.ir_model: Two fields (sale_lock_date, tax_lock_date) of res.config.settings() have the same label: Hard Lock Date. [Modules: om_fiscal_year and om_fiscal_year] 
2025-09-24 13:23:38,********** WARNING kayan_whatsapp odoo.addons.base.models.ir_model: Two fields (purchase_lock_date, tax_lock_date) of res.config.settings() have the same label: Hard Lock Date. [Modules: om_fiscal_year and om_fiscal_year] 
2025-09-24 13:23:38,********** WARNING kayan_whatsapp odoo.addons.base.models.ir_model: Two fields (hard_lock_date, tax_lock_date) of res.config.settings() have the same label: Hard Lock Date. [Modules: om_fiscal_year and om_fiscal_year] 
2025-09-24 13:23:39,********** INFO kayan_whatsapp odoo.modules.loading: loading om_fiscal_year/security/security.xml 
2025-09-24 13:23:39,********** INFO kayan_whatsapp odoo.modules.loading: loading om_fiscal_year/security/ir.model.access.csv 
2025-09-24 13:23:39,********** INFO kayan_whatsapp odoo.modules.loading: loading om_fiscal_year/wizard/change_lock_date.xml 
2025-09-24 13:23:39,********** INFO kayan_whatsapp odoo.modules.loading: loading om_fiscal_year/views/fiscal_year.xml 
2025-09-24 13:23:39,********** INFO kayan_whatsapp odoo.modules.loading: loading om_fiscal_year/views/settings.xml 
2025-09-24 13:23:39,********** INFO kayan_whatsapp odoo.addons.base.models.ir_module: module om_fiscal_year: loading translation file c:\odoo18_cubes\server\custom_addons\om_fiscal_year\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:39,********** INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\om_fiscal_year\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:39,592 127508 INFO kayan_whatsapp odoo.modules.loading: Module om_fiscal_year loaded in 0.98s, 273 queries (+273 other) 
2025-09-24 13:23:39,592 127508 INFO kayan_whatsapp odoo.modules.loading: Loading module om_recurring_payments (69/98) 
2025-09-24 13:23:39,765 127508 INFO kayan_whatsapp odoo.modules.registry: module om_recurring_payments: creating or updating database tables 
2025-09-24 13:23:40,064 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_recurring_payments/data/sequence.xml 
2025-09-24 13:23:40,101 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_recurring_payments/data/recurring_cron.xml 
2025-09-24 13:23:40,129 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_recurring_payments/security/ir.model.access.csv 
2025-09-24 13:23:40,192 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_recurring_payments/views/recurring_template_view.xml 
2025-09-24 13:23:40,272 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_recurring_payments/views/recurring_payment_view.xml 
2025-09-24 13:23:40,319 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module om_recurring_payments: loading translation file c:\odoo18_cubes\server\custom_addons\om_recurring_payments\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:40,346 127508 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\om_recurring_payments\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:40,********** INFO kayan_whatsapp odoo.modules.loading: Module om_recurring_payments loaded in 0.79s, 227 queries (+227 other) 
2025-09-24 13:23:40,********** INFO kayan_whatsapp odoo.modules.loading: Loading module om_account_daily_reports (81/98) 
2025-09-24 13:23:40,673 127508 INFO kayan_whatsapp odoo.modules.registry: module om_account_daily_reports: creating or updating database tables 
2025-09-24 13:23:41,485 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/security/ir.model.access.csv 
2025-09-24 13:23:41,526 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/views/om_daily_reports.xml 
2025-09-24 13:23:41,568 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/wizard/daybook.xml 
2025-09-24 13:23:41,638 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/wizard/cashbook.xml 
2025-09-24 13:23:41,701 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/wizard/bankbook.xml 
2025-09-24 13:23:41,755 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/report/reports.xml 
2025-09-24 13:23:41,795 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/report/report_daybook.xml 
2025-09-24 13:23:41,833 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/report/report_cashbook.xml 
2025-09-24 13:23:41,874 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_daily_reports/report/report_bankbook.xml 
2025-09-24 13:23:41,907 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module om_account_daily_reports: loading translation file c:\odoo18_cubes\server\custom_addons\om_account_daily_reports\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:41,923 127508 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\om_account_daily_reports\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:41,********** INFO kayan_whatsapp odoo.modules.loading: Module om_account_daily_reports loaded in 1.60s, 289 queries (+289 other) 
2025-09-24 13:23:41,********** INFO kayan_whatsapp odoo.modules.loading: Loading module om_account_accountant (88/98) 
2025-09-24 13:23:42,********** INFO kayan_whatsapp odoo.modules.registry: module om_account_accountant: creating or updating database tables 
2025-09-24 13:23:42,********** WARNING kayan_whatsapp odoo.addons.base.models.ir_model: Two fields (sale_lock_date, tax_lock_date) of res.config.settings() have the same label: Hard Lock Date. [Modules: om_fiscal_year and om_fiscal_year] 
2025-09-24 13:23:42,********** WARNING kayan_whatsapp odoo.addons.base.models.ir_model: Two fields (purchase_lock_date, tax_lock_date) of res.config.settings() have the same label: Hard Lock Date. [Modules: om_fiscal_year and om_fiscal_year] 
2025-09-24 13:23:42,********** WARNING kayan_whatsapp odoo.addons.base.models.ir_model: Two fields (hard_lock_date, tax_lock_date) of res.config.settings() have the same label: Hard Lock Date. [Modules: om_fiscal_year and om_fiscal_year] 
2025-09-24 13:23:42,********** INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/security/group.xml 
2025-09-24 13:23:42,********** INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/menu.xml 
2025-09-24 13:23:42,********** INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/settings.xml 
2025-09-24 13:23:42,********** INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/account_group.xml 
2025-09-24 13:23:42,********** INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/account_tag.xml 
2025-09-24 13:23:42,********** INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/res_partner.xml 
2025-09-24 13:23:42,********** INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/account_bank_statement.xml 
2025-09-24 13:23:42,714 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/payment_method.xml 
2025-09-24 13:23:42,761 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/reconciliation.xml 
2025-09-24 13:23:42,778 127508 INFO kayan_whatsapp odoo.modules.loading: loading om_account_accountant/views/account_journal.xml 
2025-09-24 13:23:42,826 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module om_account_accountant: loading translation file c:\odoo18_cubes\server\custom_addons\om_account_accountant\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:42,842 127508 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\om_account_accountant\i18n\ar_001.po for language ar_001 
2025-09-24 13:23:42,881 127508 INFO kayan_whatsapp odoo.modules.loading: Module om_account_accountant loaded in 0.89s, 398 queries (+398 other) 
2025-09-24 13:23:42,881 127508 INFO kayan_whatsapp odoo.modules.loading: 98 modules loaded in 8.65s, 2293 queries (+2293 extra) 
2025-09-24 13:23:44,541 127508 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 13:23:44,554 127508 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 13:23:44,554 127508 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 12.004s 
2025-09-24 13:23:44,554 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-24 13:23:44,564 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:44] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 5539 4.791 7.403
2025-09-24 13:23:44,604 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 13:23:46,246 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:46] "GET /odoo HTTP/1.1" 200 - 95 0.113 1.532
2025-09-24 13:23:46,628 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:46] "GET /web/webclient/load_menus/a30a1f9b6b38e22c9eee47ed59c4afbe86e1488a75edca6330799fc2af839857 HTTP/1.1" 200 - 278 0.215 0.133
2025-09-24 13:23:47,319 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:47] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.002 0.022
2025-09-24 13:23:47,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:47] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.015 0.017
2025-09-24 13:23:47,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:47] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.007 0.025
2025-09-24 13:23:47,725 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:47] "POST /mail/data HTTP/1.1" 200 - 69 0.099 0.076
2025-09-24 13:23:53,826 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-09-24 13:23:53,829 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.003s 
2025-09-24 13:23:53,841 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-09-24 13:23:53,849 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-09-24 13:23:53,868 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-09-24 13:23:53,874 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.006s 
2025-09-24 13:23:53,874 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-09-24 13:23:53,890 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-09-24 13:23:53,906 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-09-24 13:23:53,913 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.008s 
2025-09-24 13:23:53,913 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-09-24 13:23:53,922 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-09-24 13:23:53,938 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Account Asset: Generate asset entries' (55) starting 
2025-09-24 13:23:53,939 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Account Asset: Generate asset entries' (55) done in 0.002s 
2025-09-24 13:23:53,********** INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Account Asset: Generate asset entries' (55) processed 0 records, 0 records remaining 
2025-09-24 13:23:53,********** INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Account Asset: Generate asset entries' (55) completed 
2025-09-24 13:23:53,968 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (56) starting 
2025-09-24 13:23:53,968 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (56) done in 0.000s 
2025-09-24 13:23:53,984 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (56) processed 0 records, 0 records remaining 
2025-09-24 13:23:53,********** INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (56) completed 
2025-09-24 13:23:54,000 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-09-24 13:23:54,016 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.016s 
2025-09-24 13:23:54,016 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-09-24 13:23:54,016 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-09-24 13:23:54,032 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-09-24 13:23:54,048 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.016s 
2025-09-24 13:23:54,048 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-09-24 13:23:54,048 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-09-24 13:23:58,212 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:58] "POST /web/action/load HTTP/1.1" 200 - 6 0.016 0.022
2025-09-24 13:23:58,519 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:58] "GET /web/image/res.partner/3/avatar_128?unique=1758713018000 HTTP/1.1" 200 - 9 0.014 0.022
2025-09-24 13:23:58,623 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:58] "POST /mail/data HTTP/1.1" 200 - 65 0.077 0.062
2025-09-24 13:23:58,641 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:58] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.013 0.012
2025-09-24 13:23:59,022 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:59] "POST /mail/inbox/messages HTTP/1.1" 200 - 4 0.017 0.020
2025-09-24 13:23:59,069 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:23:59] "GET /web/image/res.partner/2/avatar_128?unique=************* HTTP/1.1" 200 - 9 0.049 0.027
2025-09-24 13:24:01,550 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:01] "POST /web/action/load HTTP/1.1" 200 - 10 0.016 0.033
2025-09-24 13:24:02,045 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:02] "POST /web/dataset/call_kw/account.journal/get_views HTTP/1.1" 200 - 55 0.043 0.100
2025-09-24 13:24:02,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:02] "POST /web/dataset/call_kw/account.journal/web_search_read HTTP/1.1" 200 - 38 0.058 0.042
2025-09-24 13:24:02,372 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:02] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.005
2025-09-24 13:24:02,595 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:02] "GET /web/bundle/web.chartjs_lib?lang=en_US&debug=1 HTTP/1.1" 200 - 2 0.004 0.009
2025-09-24 13:24:05,394 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:05] "POST /web/action/load HTTP/1.1" 200 - 9 0.008 0.025
2025-09-24 13:24:06,344 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:06] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 395 0.237 0.387
2025-09-24 13:24:06,645 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:06] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 204 0.158 0.106
2025-09-24 13:24:07,029 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:07] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.006 0.004
2025-09-24 13:24:07,051 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:07] "POST /base_setup/data HTTP/1.1" 200 - 6 0.005 0.010
2025-09-24 13:24:13,868 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:13] "POST /web/dataset/call_kw/res.config.settings/web_save HTTP/1.1" 200 - 63 0.108 0.062
2025-09-24 13:24:14,234 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:14] "POST /web/action/load HTTP/1.1" 200 - 12 0.016 0.015
2025-09-24 13:24:14,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:14] "POST /web/dataset/call_kw/res.users/get_views HTTP/1.1" 200 - 133 0.130 0.282
2025-09-24 13:24:14,917 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:14] "POST /web/dataset/call_kw/res.users/web_search_read HTTP/1.1" 200 - 16 0.032 0.002
2025-09-24 13:24:15,202 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:15] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.000
2025-09-24 13:24:17,900 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:17] "POST /web/dataset/call_kw/res.users/web_read HTTP/1.1" 200 - 49 0.051 0.083
2025-09-24 13:24:20,352 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:20] "GET /odoo/settings/12/users/2?debug=1 HTTP/1.1" 200 - 15 0.022 0.055
2025-09-24 13:24:20,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:20] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 3 0.002 0.069
2025-09-24 13:24:21,080 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:21] "POST /web/action/load_breadcrumbs HTTP/1.1" 200 - 14 0.042 0.084
2025-09-24 13:24:21,134 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:21] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.026 0.142
2025-09-24 13:24:21,134 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:21] "POST /mail/data HTTP/1.1" 200 - 38 0.097 0.081
2025-09-24 13:24:21,154 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:21] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.031 0.156
2025-09-24 13:24:21,435 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:21] "POST /web/action/load HTTP/1.1" 200 - 13 0.014 0.018
2025-09-24 13:24:21,719 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:21] "POST /web/dataset/call_kw/res.users/get_views HTTP/1.1" 200 - 37 0.037 0.165
2025-09-24 13:24:21,790 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:21] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.018 0.020
2025-09-24 13:24:22,188 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:22] "POST /web/dataset/call_kw/res.users/web_read HTTP/1.1" 200 - 39 0.035 0.084
2025-09-24 13:24:22,672 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:22] "GET /web/image/res.users/2/avatar_128?unique=1758720216000 HTTP/1.1" 200 - 9 0.004 0.024
2025-09-24 13:24:36,243 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 13:24:36,258 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.015s 
2025-09-24 13:24:36,258 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 13:24:36,258 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 13:24:58,983 127508 INFO kayan_whatsapp odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-09-24 13:24:58,983 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:58] "POST /web/dataset/call_kw/res.users/web_save HTTP/1.1" 200 - 115 0.132 0.195
2025-09-24 13:24:59,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:24:59] "GET /web/image/res.users/2/avatar_128?unique=1758720298000 HTTP/1.1" 200 - 21 0.021 0.037
2025-09-24 13:25:22,074 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:22] "POST /web/action/load HTTP/1.1" 200 - 10 0.017 0.006
2025-09-24 13:25:22,734 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:22] "POST /web/dataset/call_kw/res.groups/get_views HTTP/1.1" 200 - 112 0.128 0.177
2025-09-24 13:25:22,766 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:22] "POST /web/dataset/call_kw/res.groups/web_search_read HTTP/1.1" 200 - 3 0.000 0.015
2025-09-24 13:25:23,084 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:23] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.008
2025-09-24 13:25:24,774 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:24] "POST /web/dataset/call_kw/res.groups/web_search_read HTTP/1.1" 200 - 3 0.006 0.016
2025-09-24 13:25:25,451 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:25] "POST /web/dataset/call_kw/res.groups/web_search_read HTTP/1.1" 200 - 4 0.027 0.016
2025-09-24 13:25:27,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:27] "POST /web/dataset/call_kw/res.groups/web_read HTTP/1.1" 200 - 30 0.065 0.073
2025-09-24 13:25:55,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:55] "POST /web/action/load HTTP/1.1" 200 - 9 0.024 0.020
2025-09-24 13:25:56,050 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:56] "POST /web/dataset/call_kw/stock.picking.type/get_views HTTP/1.1" 200 - 41 0.073 0.110
2025-09-24 13:25:56,219 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:56] "POST /web/dataset/call_kw/stock.picking.type/web_search_read HTTP/1.1" 200 - 22 0.065 0.037
2025-09-24 13:25:56,395 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:56] "GET /web/bundle/web.chartjs_lib?lang=en_US&debug=1 HTTP/1.1" 200 - 1 0.000 0.008
2025-09-24 13:25:57,959 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:57] "POST /web/action/load HTTP/1.1" 200 - 11 0.025 0.017
2025-09-24 13:25:58,389 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:58] "POST /web/dataset/call_kw/product.category/get_views HTTP/1.1" 200 - 28 0.056 0.050
2025-09-24 13:25:58,583 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:25:58] "POST /web/dataset/call_kw/product.category/web_search_read HTTP/1.1" 200 - 3 0.039 0.000
2025-09-24 13:26:02,202 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:26:02] "POST /web/dataset/call_kw/product.category/web_read HTTP/1.1" 200 - 18 0.047 0.034
2025-09-24 13:26:02,665 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:26:02] "POST /mail/thread/data HTTP/1.1" 200 - 17 0.036 0.036
2025-09-24 13:26:02,685 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:26:02] "POST /mail/thread/messages HTTP/1.1" 200 - 22 0.027 0.049
2025-09-24 13:26:38,320 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 13:26:38,336 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.016s 
2025-09-24 13:26:38,352 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 13:26:38,352 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:27:52,718 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:52] "POST /web/action/load HTTP/1.1" 200 - 10 0.003 0.020
2025-09-24 13:27:53,137 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:53] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 50 0.092 0.084
2025-09-24 13:27:53,154 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:53] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.002 0.006
2025-09-24 13:27:53,584 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:53] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 58 0.050 0.071
2025-09-24 13:27:53,603 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:53] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.034 0.101
2025-09-24 13:27:55,069 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:55] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.063 0.039
2025-09-24 13:27:55,147 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:55] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.015 0.164
2025-09-24 13:27:57,136 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:57] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.087 0.059
2025-09-24 13:27:57,501 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:57] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.012 0.038
2025-09-24 13:27:57,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:57] "GET /account_check_printing/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-09-24 13:27:57,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:57] "GET /sale_timesheet/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-09-24 13:27:57,********** INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:57] "GET /payment_asiapay/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-09-24 13:27:59,671 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:27:59,671 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 13:27:59,671 127508 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:27:59,747 127508 WARNING kayan_whatsapp odoo.http: Unable to install module "base_accounting_kit" because an external dependency is not met: External dependency qifparse not installed: No package metadata was found for qifparse 
2025-09-24 13:27:59,747 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:27:59] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 22 0.028 0.059
2025-09-24 13:29:10,092 127508 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:29:10] "GET /web/session/logout HTTP/1.1" 303 - 4 0.017 0.025
2025-09-24 13:29:10,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:29:10] "GET /odoo HTTP/1.1" 303 - 1 0.000 0.018
2025-09-24 13:29:10,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:29:10] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 53 0.049 0.607
2025-09-24 13:29:11,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:29:11] "GET /web/binary/company_logo HTTP/1.1" 200 - 2 0.008 0.008
2025-09-24 13:29:18,********** INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1ee3ffe/web.assets_frontend.min.css (id:1149) 
2025-09-24 13:29:18,********** INFO kayan_whatsapp odoo.addons.base.models.assetsbundle: Deleting attachments [1147] (matching /web/assets/_______/web.assets_frontend.min.css) because it was replaced with /web/assets/1ee3ffe/web.assets_frontend.min.css 
2025-09-24 13:29:18,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:29:18] "GET /web/assets/1ee3ffe/web.assets_frontend.min.css HTTP/1.1" 200 - 14 0.110 7.070
2025-09-24 13:29:25,********** INFO ? odoo: Odoo version 18.0-******** 
2025-09-24 13:29:25,********** INFO ? odoo: Using configuration file at C:\odoo18_cubes\server\odoo.conf 
2025-09-24 13:29:25,********** INFO ? odoo: addons paths: ['C:\\odoo18_cubes\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18_cubes\\server\\custom_addons', 'c:\\odoo18_cubes\\server\\odoo\\addons'] 
2025-09-24 13:29:25,********** INFO ? odoo: database: openpg@localhost:5432 
2025-09-24 13:29:25,570 114052 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-09-24 13:29:25,613 114052 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-09-24 13:29:27,364 114052 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8087 
2025-09-24 13:29:28,746 114052 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-24 13:29:28,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-09-24 13:29:28,903 114052 INFO ? odoo.modules.loading: loading 98 modules... 
2025-09-24 13:29:31,043 114052 WARNING ? odoo.tools.translate: no translation language detected, skipping translation <frame at 0x000001F77734DA40, file 'c:\\odoo18_cubes\\server\\custom_addons\\om_account_followup\\wizard\\followup_print.py', line 34, code FollowupPrint> 
Stack (most recent call last):
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo18\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo18\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo18\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Odoo18\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo18\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo18\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo18_cubes\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1869, in _serve_db
    self.registry, cr_readonly = self._open_registry()
  File "C:\odoo18_cubes\server\odoo\http.py", line 1485, in _open_registry
    registry = Registry(self.db)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18_cubes\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\__init__.py", line 1, in <module>
    from . import wizard
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\__init__.py", line 1, in <module>
    from . import followup_print
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 7, in <module>
    class FollowupPrint(models.TransientModel):
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 34, in FollowupPrint
    default=_('Invoices Reminder'))
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 609, in get_text_alias
    module, lang = _get_translation_source(1)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 598, in _get_translation_source
    lang = lang or _get_lang(frame, default_lang)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 589, in _get_lang
    _logger.log(log_level, 'no translation language detected, skipping translation %s', frame, stack_info=True)
2025-09-24 13:29:31,317 114052 INFO ? odoo.modules.loading: Loading module whatsapp_evolution (79/98) 
2025-09-24 13:29:32,117 114052 INFO ? odoo.modules.registry: module whatsapp_evolution: creating or updating database tables 
2025-09-24 13:29:32,464 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/security/whatsapp_evolution_security.xml 
2025-09-24 13:29:32,494 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/security/ir.model.access.csv 
2025-09-24 13:29:33,728 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_settings_data.xml 
2025-09-24 13:29:33,734 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_data.xml 
2025-09-24 13:29:33,********** INFO ? odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_default_data.xml 
2025-09-24 13:29:33,753 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_config_views.xml 
2025-09-24 13:29:33,890 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_settings_views.xml 
2025-09-24 13:29:33,934 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_normal_user_views.xml 
2025-09-24 13:29:34,004 114052 INFO ? odoo.models.unlink: User #1 deleted ir.actions.act_window.view records with IDs: [194, 195] 
2025-09-24 13:29:34,********** INFO ? odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_message_views.xml 
2025-09-24 13:29:34,091 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_send_message_wizard_views.xml 
2025-09-24 13:29:34,145 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_instance_wizard_views.xml 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,167 114052 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-whatsapp text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 26,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,173 114052 WARNING ? odoo.addons.base.models.ir_ui_view: An alert (class alert-*) must have an alert, alertdialog or status role or an alert-link class. Please use alert and alertdialog only for what expects to stop any activity to be read immediately.
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 74,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,173 114052 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-check-circle text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 116,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,177 114052 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-exclamation-triangle text-danger) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 137,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 13:29:34,186 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_business_template_views.xml 
2025-09-24 13:29:34,253 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/wizard/whatsapp_sms_test_wizard_views.xml 
2025-09-24 13:29:34,285 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/views/menu_views.xml 
2025-09-24 13:29:34,467 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_business_template_data.xml 
2025-09-24 13:29:34,487 114052 INFO ? odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_cron_data.xml 
2025-09-24 13:29:34,512 114052 INFO ? odoo.addons.base.models.ir_module: module whatsapp_evolution: loading translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 13:29:34,512 114052 INFO ? odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 13:29:34,689 114052 INFO ? odoo.modules.loading: Module whatsapp_evolution loaded in 3.37s, 552 queries (+552 other) 
2025-09-24 13:29:35,261 114052 INFO ? odoo.modules.loading: 98 modules loaded in 6.36s, 552 queries (+552 extra) 
2025-09-24 13:29:36,038 114052 INFO ? odoo.modules.registry: verifying fields for every extended model 
2025-09-24 13:29:36,129 114052 INFO ? odoo.modules.loading: Modules loaded. 
2025-09-24 13:29:36,138 114052 INFO ? odoo.modules.registry: Caches invalidated, signaling through the database: ['default', 'templates'] 
2025-09-24 13:29:36,146 114052 INFO ? odoo.modules.registry: Registry loaded in 7.540s 
2025-09-24 13:29:36,150 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 13:29:38,007 114052 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:29:38] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 55 0.230 8.800
2025-09-24 13:29:38,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:29:38] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 1138 1.558 7.853
2025-09-24 13:29:39,********** WARNING ? odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 13:29:40,********** INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:29:40] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.018 2.037
2025-09-24 13:30:00,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=aljail_live host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:00,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,843 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,843 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,843 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,843 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,843 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,843 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,851 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,851 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,851 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,851 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,856 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,858 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,859 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,861 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,909 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,911 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,912 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,913 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,915 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,917 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,917 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,917 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,917 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,917 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,921 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,921 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,921 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,921 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,921 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,929 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,929 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,929 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,934 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,935 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:00,938 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:00,940 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:00,942 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:00,943 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:00,947 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,011 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,013 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,016 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=linda host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=martinlion_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=matrinlive host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,********** INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,030 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,030 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,034 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,039 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=taibat host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,040 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,044 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,046 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,049 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,052 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,054 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,057 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,060 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=3/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:01,227 114052 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:30:01] "GET /web/database/manager HTTP/1.1" 200 - 238 8.912 9.945
2025-09-24 13:30:12,134 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,135 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,135 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,139 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,139 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,139 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,139 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,139 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,139 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,147 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,147 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,147 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,151 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,151 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,151 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,156 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,156 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,156 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,156 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,156 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,156 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,164 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,164 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,164 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,167 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,167 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,167 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,167 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,167 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,167 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,167 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,167 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,180 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,180 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,180 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:12,180 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_5 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,184 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_6 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,185 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_67 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,186 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_8 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,186 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=fania_1 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,189 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,189 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,189 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,189 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,189 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,189 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,189 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,196 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=linda host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,198 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=martinlion_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,198 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=matrinlive host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,201 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,201 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,203 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,204 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,204 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=taibat host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,204 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,204 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,204 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,209 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,210 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,210 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,211 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,212 114052 INFO kayan_whatsapp odoo.sql_db: ConnectionPool(read/write;used=2/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:12,333 114052 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 13:30:12] "GET /web/database/manager HTTP/1.1" 200 - 238 3.522 9.064
2025-09-24 13:30:35,798 114052 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "C:\odoo18_cubes\server\odoo\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
  File "C:\odoo18_cubes\server\odoo\http.py", line 398, in dispatch_rpc
    return dispatch(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18_cubes\server\odoo\service\db.py", line 494, in dispatch
    check_super(passwd)
  File "C:\odoo18_cubes\server\odoo\service\db.py", line 60, in check_super
    raise odoo.exceptions.AccessDenied()
odoo.exceptions.AccessDenied: Access Denied
2025-09-24 13:30:48,237 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,237 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,237 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,247 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,247 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,247 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,251 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,********** INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,254 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,254 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,254 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,258 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,258 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,260 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,260 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,260 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,260 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,260 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,267 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,269 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,269 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,271 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,271 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,271 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,271 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,271 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,278 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,278 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,278 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,278 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,284 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,286 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,287 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,287 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,289 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,291 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=64/max=64): Closed 0 connections  
2025-09-24 13:30:48,293 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_6 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,293 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_67 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,296 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_8 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,296 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=fania_1 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,301 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,301 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,304 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,307 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,309 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,312 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,316 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,317 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=linda host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,319 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=martinlion_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,323 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=matrinlive host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,326 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,327 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,331 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,334 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,********** INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=taibat host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,********** INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,342 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,342 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,347 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,348 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,351 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,354 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,354 114052 INFO None odoo.sql_db: ConnectionPool(read/write;used=3/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-114052 sslmode=prefer' 
2025-09-24 13:30:48,485 114052 INFO None werkzeug: 127.0.0.1 - - [24/Sep/2025 13:30:48] "POST /web/database/create HTTP/1.1" 200 - 238 3.524 9.194
2025-09-24 13:31:21,327 114052 INFO None odoo.service.db: Create database `kayan_2`. 
2025-09-24 13:31:25,********** INFO None odoo.modules.loading: loading 1 modules... 
2025-09-24 13:31:25,********** INFO None odoo.modules.loading: Loading module base (1/1) 
2025-09-24 13:31:25,167 114052 INFO None odoo.modules.registry: module base: creating or updating database tables 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of ir.module.module.menus_by_module 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of ir.module.module.reports_by_module 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of ir.module.module.views_by_module 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.partner.user_id 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.partner.commercial_partner_id 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.partner.complete_name 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.partner.company_registry 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.partner.commercial_company_name 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.partner.partner_share 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.currency.decimal_places 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.company.uses_default_logo 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.company.logo_web 
2025-09-24 13:31:26,********** INFO None odoo.models: Computing parent_path for table res_company... 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.users.signature 
2025-09-24 13:31:26,********** INFO None odoo.models: Prepare computation of res.users.share 
2025-09-24 13:31:30,********** INFO None odoo.modules.loading: loading base/data/res_bank.xml 
2025-09-24 13:31:30,********** INFO None odoo.modules.loading: loading base/data/res.lang.csv 
2025-09-24 13:31:30,********** INFO None odoo.modules.loading: loading base/data/res_lang_data.xml 
2025-09-24 13:31:30,********** INFO None odoo.modules.loading: loading base/data/res_partner_data.xml 
2025-09-24 13:31:31,********** INFO None odoo.modules.loading: loading base/data/res_currency_data.xml 
2025-09-24 13:31:32,********** INFO None odoo.modules.loading: loading base/data/res_company_data.xml 
2025-09-24 13:31:32,********** INFO None odoo.modules.loading: loading base/data/res_users_data.xml 
2025-09-24 13:31:35,********** INFO None odoo.modules.loading: loading base/data/report_paperformat_data.xml 
2025-09-24 13:31:35,********** INFO None odoo.modules.loading: loading base/data/res_country_data.xml 
2025-09-24 13:31:36,********** INFO None odoo.modules.loading: loading base/data/ir_demo_data.xml 
2025-09-24 13:31:37,********** INFO None odoo.modules.loading: loading base/security/base_groups.xml 
2025-09-24 13:31:37,********** INFO None odoo.modules.loading: loading base/security/base_security.xml 
2025-09-24 13:31:38,********** INFO None odoo.modules.loading: loading base/views/base_menus.xml 
2025-09-24 13:31:38,********** INFO None odoo.modules.loading: loading base/views/decimal_precision_views.xml 
2025-09-24 13:31:38,********** INFO None odoo.modules.loading: loading base/views/res_config_views.xml 
2025-09-24 13:31:38,********** INFO None odoo.modules.loading: loading base/data/res.country.state.csv 
2025-09-24 13:31:40,370 114052 INFO None odoo.modules.loading: loading base/views/ir_actions_views.xml 
2025-09-24 13:31:40,817 114052 INFO None odoo.modules.loading: loading base/views/ir_asset_views.xml 
2025-09-24 13:31:40,894 114052 INFO None odoo.modules.loading: loading base/views/ir_config_parameter_views.xml 
2025-09-24 13:31:40,972 114052 INFO None odoo.modules.loading: loading base/views/ir_cron_views.xml 
2025-09-24 13:31:41,111 114052 INFO None odoo.modules.loading: loading base/views/ir_cron_trigger_views.xml 
2025-09-24 13:31:41,170 114052 INFO None odoo.modules.loading: loading base/views/ir_filters_views.xml 
2025-09-24 13:31:41,269 114052 INFO None odoo.modules.loading: loading base/views/ir_mail_server_views.xml 
2025-09-24 13:31:41,351 114052 INFO None odoo.modules.loading: loading base/views/ir_model_views.xml 
2025-09-24 13:31:41,784 114052 INFO None odoo.modules.loading: loading base/views/ir_attachment_views.xml 
2025-09-24 13:31:41,872 114052 INFO None odoo.modules.loading: loading base/views/ir_rule_views.xml 
2025-09-24 13:31:41,951 114052 INFO None odoo.modules.loading: loading base/views/ir_sequence_views.xml 
2025-09-24 13:31:42,036 114052 INFO None odoo.modules.loading: loading base/views/ir_ui_menu_views.xml 
2025-09-24 13:31:42,123 114052 INFO None odoo.modules.loading: loading base/views/ir_ui_view_views.xml 
2025-09-24 13:31:42,294 114052 INFO None odoo.modules.loading: loading base/views/ir_default_views.xml 
2025-09-24 13:31:42,391 114052 INFO None odoo.modules.loading: loading base/data/ir_config_parameter_data.xml 
2025-09-24 13:31:42,403 114052 INFO None odoo.modules.loading: loading base/data/ir_cron_data.xml 
2025-09-24 13:31:42,463 114052 INFO None odoo.modules.loading: loading base/report/ir_model_report.xml 
2025-09-24 13:31:42,499 114052 INFO None odoo.modules.loading: loading base/report/ir_model_templates.xml 
2025-09-24 13:31:42,545 114052 INFO None odoo.modules.loading: loading base/views/ir_logging_views.xml 
2025-09-24 13:31:42,617 114052 INFO None odoo.modules.loading: loading base/views/ir_qweb_widget_templates.xml 
2025-09-24 13:31:42,669 114052 INFO None odoo.modules.loading: loading base/views/ir_module_views.xml 
2025-09-24 13:31:42,870 114052 INFO None odoo.modules.loading: loading base/data/ir_module_category_data.xml 
2025-09-24 13:31:43,095 114052 INFO None odoo.modules.loading: loading base/data/ir_module_module.xml 
2025-09-24 13:31:43,272 114052 INFO None odoo.modules.loading: loading base/report/ir_module_reports.xml 
2025-09-24 13:31:43,318 114052 INFO None odoo.modules.loading: loading base/report/ir_module_report_templates.xml 
2025-09-24 13:31:43,339 114052 INFO None odoo.modules.loading: loading base/wizard/base_module_update_views.xml 
2025-09-24 13:31:43,403 114052 INFO None odoo.modules.loading: loading base/wizard/base_language_install_views.xml 
2025-09-24 13:31:43,451 114052 INFO None odoo.modules.loading: loading base/wizard/base_import_language_views.xml 
2025-09-24 13:31:43,506 114052 INFO None odoo.modules.loading: loading base/wizard/base_module_upgrade_views.xml 
2025-09-24 13:31:43,569 114052 INFO None odoo.modules.loading: loading base/wizard/base_module_uninstall_views.xml 
2025-09-24 13:31:43,********** INFO None odoo.modules.loading: loading base/wizard/base_export_language_views.xml 
2025-09-24 13:31:43,********** INFO None odoo.modules.loading: loading base/wizard/base_partner_merge_views.xml 
2025-09-24 13:31:43,********** INFO None odoo.modules.loading: loading base/data/ir_demo_failure_data.xml 
2025-09-24 13:31:43,********** INFO None odoo.modules.loading: loading base/views/ir_profile_views.xml 
2025-09-24 13:31:43,********** INFO None odoo.modules.loading: loading base/views/res_company_views.xml 
2025-09-24 13:31:44,********** INFO None odoo.modules.loading: loading base/views/res_lang_views.xml 
2025-09-24 13:31:44,********** INFO None odoo.modules.loading: loading base/views/res_partner_views.xml 
2025-09-24 13:31:44,********** INFO None odoo.modules.loading: loading base/views/res_bank_views.xml 
2025-09-24 13:31:44,********** INFO None odoo.modules.loading: loading base/views/res_country_views.xml 
2025-09-24 13:31:44,********** INFO None odoo.modules.loading: loading base/views/res_currency_views.xml 
2025-09-24 13:31:44,********** INFO None odoo.modules.loading: loading base/views/res_users_views.xml 
2025-09-24 13:31:45,********** INFO None odoo.modules.loading: loading base/views/res_device_views.xml 
2025-09-24 13:31:45,********** INFO None odoo.modules.loading: loading base/views/res_users_identitycheck_views.xml 
2025-09-24 13:31:45,********** INFO None odoo.modules.loading: loading base/views/res_config_settings_views.xml 
2025-09-24 13:31:45,333 114052 INFO None odoo.modules.loading: loading base/views/report_paperformat_views.xml 
2025-09-24 13:31:45,409 114052 INFO None odoo.modules.loading: loading base/security/ir.model.access.csv 
2025-09-24 13:31:45,720 114052 INFO None odoo.modules.loading: Module base: loading demo 
2025-09-24 13:31:45,720 114052 INFO None odoo.modules.loading: loading base/data/res_users_demo.xml 
2025-09-24 13:31:50,311 114052 INFO None odoo.modules.loading: loading base/data/res_partner_bank_demo.xml 
2025-09-24 13:31:50,343 114052 INFO None odoo.modules.loading: loading base/data/res_currency_demo.xml 
2025-09-24 13:31:50,380 114052 INFO None odoo.modules.loading: loading base/data/res_currency_rate_demo.xml 
2025-09-24 13:31:51,137 114052 INFO None odoo.modules.loading: loading base/data/res_bank_demo.xml 
2025-09-24 13:31:51,168 114052 INFO None odoo.modules.loading: loading base/data/res_partner_demo.xml 
2025-09-24 13:31:51,717 114052 INFO None odoo.modules.loading: loading base/data/res_partner_image_demo.xml 
2025-09-24 13:31:53,906 114052 INFO None odoo.modules.loading: Module base loaded in 28.80s, 9218 queries (+9226 other) 
2025-09-24 13:31:53,906 114052 INFO None odoo.modules.loading: 1 modules loaded in 28.80s, 9218 queries (+9226 extra) 
2025-09-24 13:31:54,004 114052 INFO None odoo.modules.loading: updating modules list 
2025-09-24 13:31:54,004 114052 INFO None odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-09-24 13:31:56,128 114052 INFO None odoo.modules.loading: loading 13 modules... 
2025-09-24 13:31:56,128 114052 INFO None odoo.modules.loading: Loading module web (2/13) 
2025-09-24 13:31:56,187 114052 INFO None odoo.modules.registry: module web: creating or updating database tables 
2025-09-24 13:31:58,084 114052 INFO None odoo.modules.loading: loading web/security/ir.model.access.csv 
2025-09-24 13:31:58,134 114052 INFO None odoo.modules.loading: loading web/views/webclient_templates.xml 
2025-09-24 13:31:58,435 114052 INFO None odoo.modules.loading: loading web/views/report_templates.xml 
2025-09-24 13:31:58,758 114052 INFO None odoo.modules.loading: loading web/views/base_document_layout_views.xml 
2025-09-24 13:31:58,818 114052 INFO None odoo.modules.loading: loading web/views/partner_view.xml 
2025-09-24 13:31:58,847 114052 INFO None odoo.modules.loading: loading web/views/speedscope_template.xml 
2025-09-24 13:31:58,913 114052 INFO None odoo.modules.loading: loading web/views/neutralize_views.xml 
2025-09-24 13:31:58,945 114052 INFO None odoo.modules.loading: loading web/data/ir_attachment.xml 
2025-09-24 13:31:58,978 114052 INFO None odoo.modules.loading: loading web/data/report_layout.xml 
2025-09-24 13:31:59,130 114052 INFO None odoo.modules.loading: Module web loaded in 3.00s, 1111 queries (+1111 other) 
2025-09-24 13:31:59,130 114052 INFO None odoo.modules.loading: Loading module auth_totp (3/13) 
2025-09-24 13:31:59,168 114052 INFO None odoo.modules.registry: module auth_totp: creating or updating database tables 
2025-09-24 13:31:59,352 114052 INFO None odoo.modules.loading: loading auth_totp/security/security.xml 
2025-09-24 13:31:59,466 114052 INFO None odoo.modules.loading: loading auth_totp/security/ir.model.access.csv 
2025-09-24 13:31:59,502 114052 INFO None odoo.modules.loading: loading auth_totp/data/ir_action_data.xml 
2025-09-24 13:31:59,551 114052 INFO None odoo.modules.loading: loading auth_totp/views/res_users_views.xml 
2025-09-24 13:31:59,680 114052 INFO None odoo.modules.loading: loading auth_totp/views/templates.xml 
2025-09-24 13:31:59,720 114052 INFO None odoo.modules.loading: loading auth_totp/wizard/auth_totp_wizard_views.xml 
2025-09-24 13:31:59,801 114052 INFO None odoo.modules.loading: Module auth_totp loaded in 0.67s, 188 queries (+188 other) 
2025-09-24 13:31:59,801 114052 INFO None odoo.modules.loading: Loading module base_import (4/13) 
2025-09-24 13:31:59,857 114052 INFO None odoo.modules.registry: module base_import: creating or updating database tables 
2025-09-24 13:32:01,334 114052 INFO None odoo.modules.loading: loading base_import/security/ir.model.access.csv 
2025-09-24 13:32:01,419 114052 INFO None odoo.modules.loading: Module base_import loaded in 1.62s, 888 queries (+888 other) 
2025-09-24 13:32:01,419 114052 INFO None odoo.modules.loading: Loading module base_import_module (5/13) 
2025-09-24 13:32:01,471 114052 INFO None odoo.modules.registry: module base_import_module: creating or updating database tables 
2025-09-24 13:32:01,679 114052 INFO None odoo.modules.loading: loading base_import_module/security/ir.model.access.csv 
2025-09-24 13:32:01,734 114052 INFO None odoo.modules.loading: loading base_import_module/views/base_import_module_view.xml 
2025-09-24 13:32:01,815 114052 INFO None odoo.modules.loading: loading base_import_module/views/ir_module_views.xml 
2025-09-24 13:32:01,937 114052 INFO None odoo.modules.loading: Module base_import_module loaded in 0.52s, 164 queries (+164 other) 
2025-09-24 13:32:01,937 114052 INFO None odoo.modules.loading: Loading module base_setup (6/13) 
2025-09-24 13:32:01,984 114052 INFO None odoo.modules.registry: module base_setup: creating or updating database tables 
2025-09-24 13:32:02,162 114052 INFO None odoo.modules.loading: loading base_setup/data/base_setup_data.xml 
2025-09-24 13:32:02,203 114052 INFO None odoo.modules.loading: loading base_setup/views/res_config_settings_views.xml 
2025-09-24 13:32:02,333 114052 INFO None odoo.modules.loading: loading base_setup/views/res_partner_views.xml 
2025-09-24 13:32:02,419 114052 INFO None odoo.modules.loading: Module base_setup loaded in 0.48s, 150 queries (+150 other) 
2025-09-24 13:32:02,419 114052 INFO None odoo.modules.loading: Loading module bus (7/13) 
2025-09-24 13:32:02,473 114052 INFO None odoo.modules.registry: module bus: creating or updating database tables 
2025-09-24 13:32:02,807 114052 INFO None odoo.modules.loading: loading bus/security/ir.model.access.csv 
2025-09-24 13:32:02,870 114052 INFO None odoo.modules.loading: Module bus loaded in 0.45s, 165 queries (+165 other) 
2025-09-24 13:32:02,876 114052 INFO None odoo.modules.loading: Loading module nati_arabic_font (8/13) 
2025-09-24 13:32:02,982 114052 INFO None odoo.modules.loading: Module nati_arabic_font loaded in 0.11s, 12 queries (+12 other) 
2025-09-24 13:32:02,984 114052 INFO None odoo.modules.loading: Loading module web_tour (9/13) 
2025-09-24 13:32:03,035 114052 INFO None odoo.modules.registry: module web_tour: creating or updating database tables 
2025-09-24 13:32:03,051 114052 INFO None odoo.models: Prepare computation of res.users.tour_enabled 
2025-09-24 13:32:03,309 114052 INFO None odoo.modules.loading: loading web_tour/security/ir.model.access.csv 
2025-09-24 13:32:03,366 114052 INFO None odoo.modules.loading: loading web_tour/views/tour_views.xml 
2025-09-24 13:32:03,480 114052 INFO None odoo.modules.loading: loading web_tour/views/res_users_views.xml 
2025-09-24 13:32:03,584 114052 INFO None odoo.modules.loading: Module web_tour loaded in 0.60s, 188 queries (+188 other) 
2025-09-24 13:32:03,584 114052 INFO None odoo.modules.loading: Loading module html_editor (10/13) 
2025-09-24 13:32:03,648 114052 INFO None odoo.modules.registry: module html_editor: creating or updating database tables 
2025-09-24 13:32:03,779 114052 INFO None odoo.modules.loading: Module html_editor loaded in 0.20s, 42 queries (+42 other) 
2025-09-24 13:32:03,779 114052 INFO None odoo.modules.loading: Loading module iap (11/13) 
2025-09-24 13:32:03,832 114052 INFO None odoo.modules.registry: module iap: creating or updating database tables 
2025-09-24 13:32:04,056 114052 INFO None odoo.modules.loading: loading iap/data/services.xml 
2025-09-24 13:32:04,086 114052 INFO None odoo.modules.loading: loading iap/security/ir.model.access.csv 
2025-09-24 13:32:04,147 114052 INFO None odoo.modules.loading: loading iap/security/ir_rule.xml 
2025-09-24 13:32:04,196 114052 INFO None odoo.modules.loading: loading iap/views/iap_views.xml 
2025-09-24 13:32:04,286 114052 INFO None odoo.modules.loading: loading iap/views/res_config_settings.xml 
2025-09-24 13:32:04,385 114052 INFO None odoo.modules.loading: Module iap loaded in 0.61s, 183 queries (+183 other) 
2025-09-24 13:32:04,385 114052 INFO None odoo.modules.loading: Loading module web_editor (12/13) 
2025-09-24 13:32:04,464 114052 INFO None odoo.modules.registry: module web_editor: creating or updating database tables 
2025-09-24 13:32:06,137 114052 INFO None odoo.modules.loading: loading web_editor/security/ir.model.access.csv 
2025-09-24 13:32:06,185 114052 INFO None odoo.modules.loading: loading web_editor/data/editor_assets.xml 
2025-09-24 13:32:06,208 114052 INFO None odoo.modules.loading: loading web_editor/views/editor.xml 
2025-09-24 13:32:06,284 114052 INFO None odoo.modules.loading: loading web_editor/views/snippets.xml 
2025-09-24 13:32:06,459 114052 INFO None odoo.modules.loading: Module web_editor loaded in 2.07s, 1018 queries (+1018 other) 
2025-09-24 13:32:06,459 114052 INFO None odoo.modules.loading: Loading module web_unsplash (13/13) 
2025-09-24 13:32:06,518 114052 INFO None odoo.modules.registry: module web_unsplash: creating or updating database tables 
2025-09-24 13:32:06,651 114052 INFO None odoo.modules.loading: loading web_unsplash/views/res_config_settings_view.xml 
2025-09-24 13:32:06,********** INFO None odoo.modules.loading: Module web_unsplash loaded in 0.29s, 98 queries (+98 other) 
2025-09-24 13:32:06,********** INFO None odoo.modules.loading: 13 modules loaded in 10.62s, 4207 queries (+4207 extra) 
2025-09-24 13:32:07,521 114052 INFO None odoo.modules.loading: Modules loaded. 
2025-09-24 13:32:07,********** INFO None odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 13:32:07,538 114052 INFO None odoo.modules.registry: Registry loaded in 42.470s 
2025-09-24 13:32:07,770 114052 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo18\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo18\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo18\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 496, in target
    self.cron_thread(i)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 477, in cron_thread
    ir_cron._process_jobs(db_name)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_cron.py", line 152, in _process_jobs
    registry[cls._name]._process_job(db, cron_cr, job)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_cron.py", line 367, in _process_job
    status = cls._run_job(job)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_cron.py", line 422, in _run_job
    cron, progress = cron._add_progress(timed_out_counter=timed_out_counter)
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_cron.py", line 724, in _add_progress
    progress = self.env['ir.cron.progress'].create([{
  File "<decorator-gen-0>", line 2, in create
  File "C:\odoo18_cubes\server\odoo\api.py", line 480, in _model_create_multi
    return create(self, arg)
  File "C:\odoo18_cubes\server\odoo\models.py", line 4957, in create
    records = self._create(data_list)
  File "C:\odoo18_cubes\server\odoo\models.py", line 5194, in _create
    records.modified(self._fields, create=True)
  File "C:\odoo18_cubes\server\odoo\models.py", line 7107, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo18_cubes\server\odoo\models.py", line 7158, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 420, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 13:32:07,830 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 13:32:07,871 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.036s 
2025-09-24 13:32:07,879 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 13:32:07,951 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:32:10,934 114052 INFO None odoo.addons.base.models.res_users: Login successful for db:kayan_2 login:admin from 127.0.0.1 
2025-09-24 13:32:10,988 114052 INFO None werkzeug: 127.0.0.1 - - [24/Sep/2025 13:32:10] "POST /web/database/create HTTP/1.1" 303 - 19892 23.416 27.731
2025-09-24 13:32:11,********** INFO kayan_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 13:32:11,********** INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:32:11] "GET /odoo HTTP/1.1" 303 - 4 0.005 0.067
2025-09-24 13:32:12,********** INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:32:12] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 51 0.071 0.472
2025-09-24 13:32:21,********** INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:32:21] "GET /web/binary/company_logo HTTP/1.1" 200 - 2 0.013 0.156
2025-09-24 13:32:21,********** INFO kayan_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1640cb9/web.assets_frontend.min.css (id:193) 
2025-09-24 13:32:22,********** INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:32:22] "GET /web/assets/1640cb9/web.assets_frontend.min.css HTTP/1.1" 200 - 12 0.051 9.839
2025-09-24 13:32:25,********** INFO kayan_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/bfd606d/web.assets_frontend_lazy.min.js (id:194) 
2025-09-24 13:32:25,********** INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:32:25] "GET /web/assets/bfd606d/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 9 0.021 3.349
2025-09-24 13:32:25,********** INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:32:25] "GET /web/webclient/translations/1758720745835 HTTP/1.1" 200 - 2 0.000 0.026
2025-09-24 13:32:30,********** INFO kayan_2 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-09-24 13:32:30,911 114052 INFO kayan_2 odoo.addons.base.models.ir_attachment: filestore gc 59 checked, 0 removed 
2025-09-24 13:32:31,403 114052 INFO kayan_2 odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-09-24 13:32:31,434 114052 INFO kayan_2 odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-09-24 13:32:31,435 114052 INFO kayan_2 odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-09-24 13:32:31,450 114052 INFO kayan_2 odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-09-24 13:32:31,482 114052 INFO kayan_2 odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-09-24 13:32:31,499 114052 INFO kayan_2 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.715s 
2025-09-24 13:32:31,518 114052 INFO kayan_2 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-09-24 13:32:31,531 114052 INFO kayan_2 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-09-24 13:32:31,547 114052 INFO kayan_2 odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) starting 
2025-09-24 13:32:31,562 114052 INFO kayan_2 odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) done in 0.015s 
2025-09-24 13:32:31,562 114052 INFO kayan_2 odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) processed 0 records, 0 records remaining 
2025-09-24 13:32:31,562 114052 INFO kayan_2 odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) completed 
2025-09-24 13:33:17,418 114052 INFO kayan_2 odoo.addons.base.models.res_users: Login successful for db:kayan_2 login:admin from 127.0.0.1 
2025-09-24 13:33:17,437 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:17] "POST /web/login HTTP/1.1" 303 - 29 0.053 1.600
2025-09-24 13:33:17,485 114052 INFO kayan_2 odoo.addons.base.models.res_device: User 2 inserts device log (R-nq6aI8oqUsRP5L1vQfLvXhwj72xu78IsZlFWdC1y) 
2025-09-24 13:33:18,575 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:18] "GET /odoo HTTP/1.1" 200 - 62 0.119 1.001
2025-09-24 13:33:49,550 114052 INFO kayan_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/4053a2d/web.assets_web.min.js (id:195) 
2025-09-24 13:33:51,********** INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:51] "GET /web/assets/4053a2d/web.assets_web.min.js HTTP/1.1" 200 - 12 0.018 32.637
2025-09-24 13:33:51,890 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:51] "POST /web/action/load HTTP/1.1" 200 - 10 0.045 0.082
2025-09-24 13:33:51,890 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:51] "GET /web/image/res.partner/3/avatar_128?unique=1758713529000 HTTP/1.1" 200 - 8 0.013 0.097
2025-09-24 13:33:51,948 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:51] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.031 0.133
2025-09-24 13:33:52,055 114052 INFO kayan_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/aec8791/bus.websocket_worker_assets.min.js (id:196) 
2025-09-24 13:33:52,159 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:52] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 8 0.011 0.364
2025-09-24 13:33:52,273 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:52] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.010 0.025
2025-09-24 13:33:52,295 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:52] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 57 0.145 0.125
2025-09-24 13:33:52,341 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:52] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.009 0.006
2025-09-24 13:33:52,528 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:52] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.016
2025-09-24 13:33:52,769 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:52] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.081 0.060
2025-09-24 13:33:52,820 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:52] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.010 0.183
2025-09-24 13:33:52,840 114052 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-09-24 13:33:56,325 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:56] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.050 0.053
2025-09-24 13:33:56,618 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:56] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.006 0.083
2025-09-24 13:33:57,952 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:57] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.065 0.071
2025-09-24 13:33:58,102 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:33:58] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.009 0.012
2025-09-24 13:34:00,872 114052 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:34:00,872 114052 INFO kayan_2 odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 13:34:00,883 114052 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:34:01,103 114052 WARNING kayan_2 odoo.http: Unable to install module "base_accounting_kit" because an external dependency is not met: External dependency qifparse not installed: No package metadata was found for qifparse 
2025-09-24 13:34:01,103 114052 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:34:01] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 61 0.050 0.195
2025-09-24 13:34:08,032 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 13:34:08,********** INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.032s 
2025-09-24 13:34:08,070 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 13:34:08,113 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 13:36:35,720 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 13:36:35,734 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.013s 
2025-09-24 13:36:35,734 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 13:36:35,749 114052 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 13:38:23,475 125444 INFO ? odoo: Odoo version 18.0-******** 
2025-09-24 13:38:23,475 125444 INFO ? odoo: Using configuration file at C:\odoo18_cubes\server\odoo.conf 
2025-09-24 13:38:23,475 125444 INFO ? odoo: addons paths: ['C:\\odoo18_cubes\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18_cubes\\server\\custom_addons', 'c:\\odoo18_cubes\\server\\odoo\\addons'] 
2025-09-24 13:38:23,475 125444 INFO ? odoo: database: openpg@localhost:5432 
2025-09-24 13:38:23,649 125444 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-09-24 13:38:23,675 125444 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-09-24 13:38:25,284 125444 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8087 
2025-09-24 13:38:25,730 125444 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-24 13:38:25,827 125444 INFO ? odoo.modules.loading: 1 modules loaded in 0.10s, 0 queries (+0 extra) 
2025-09-24 13:38:25,867 125444 INFO ? odoo.modules.loading: loading 13 modules... 
2025-09-24 13:38:26,742 125444 INFO ? odoo.modules.loading: 13 modules loaded in 0.88s, 0 queries (+0 extra) 
2025-09-24 13:38:26,770 125444 INFO ? odoo.modules.loading: Modules loaded. 
2025-09-24 13:38:26,773 125444 INFO ? odoo.modules.registry: Registry loaded in 1.137s 
2025-09-24 13:38:26,773 125444 INFO kayan_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 13:38:26,819 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:26] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 4 0.014 0.683
2025-09-24 13:38:27,140 125444 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-09-24 13:38:27,171 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:27] "GET /odoo/apps HTTP/1.1" 200 - 93 0.123 1.413
2025-09-24 13:38:27,352 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:27] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.000 0.009
2025-09-24 13:38:27,385 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:27] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.003 0.012
2025-09-24 13:38:27,719 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:27] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.016
2025-09-24 13:38:27,752 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:27] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.044 0.005
2025-09-24 13:38:28,175 125444 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 13:38:28,254 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:28] "POST /web/action/load HTTP/1.1" 200 - 12 0.006 0.563
2025-09-24 13:38:28,669 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:28] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.047 0.051
2025-09-24 13:38:28,853 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:28] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.008
2025-09-24 13:38:29,102 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:29] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.063 0.041
2025-09-24 13:38:29,140 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:29] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.015 0.128
2025-09-24 13:38:30,317 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:30] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.046 0.050
2025-09-24 13:38:30,632 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:30] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.014 0.081
2025-09-24 13:38:40,736 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:40] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.089 0.065
2025-09-24 13:38:40,852 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:40] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.009 0.010
2025-09-24 13:38:41,690 125444 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:38:41,690 125444 INFO kayan_2 odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 13:38:41,690 125444 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:38:41,881 125444 WARNING kayan_2 odoo.http: Unable to install module "base_accounting_kit" because an external dependency is not met: External dependency qifparse not installed: No package metadata was found for qifparse 
2025-09-24 13:38:41,881 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:41] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 61 0.044 0.157
2025-09-24 13:38:51,184 125444 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:38:51,184 125444 INFO kayan_2 odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 13:38:51,186 125444 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 13:38:51,311 125444 WARNING kayan_2 odoo.http: Unable to install module "base_accounting_kit" because an external dependency is not met: External dependency qifparse not installed: No package metadata was found for qifparse 
2025-09-24 13:38:51,311 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:38:51] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 55 0.040 0.102
2025-09-24 13:43:19,962 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:43:19] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.008
2025-09-24 13:43:20,256 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 13:43:20] "GET /odoo/offline HTTP/1.1" 200 - 6 0.009 0.039
2025-09-24 15:26:38,580 125444 INFO kayan_2 odoo.addons.base.models.res_device: User 2 inserts device log (R-nq6aI8oqUsRP5L1vQfLvXhwj72xu78IsZlFWdC1y) 
2025-09-24 15:26:38,592 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 15:26:38] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 2 0.004 0.030
2025-09-24 20:27:24,206 125444 INFO kayan_2 odoo.addons.base.models.res_device: User 2 inserts device log (R-nq6aI8oqUsRP5L1vQfLvXhwj72xu78IsZlFWdC1y) 
2025-09-24 20:27:24,215 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:27:24] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 2 0.008 0.031
2025-09-24 20:29:37,543 125444 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 20:29:37,543 125444 INFO kayan_2 odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 20:29:37,543 125444 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 20:29:37,615 125444 WARNING kayan_2 odoo.http: Unable to install module "base_accounting_kit" because an external dependency is not met: External dependency qifparse not installed: No package metadata was found for qifparse 
2025-09-24 20:29:37,615 125444 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:29:37] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 55 0.028 0.051
2025-09-24 20:31:10,158 112708 INFO ? odoo: Odoo version 18.0-******** 
2025-09-24 20:31:10,158 112708 INFO ? odoo: Using configuration file at C:\odoo18_cubes\server\odoo.conf 
2025-09-24 20:31:10,158 112708 INFO ? odoo: addons paths: ['C:\\odoo18_cubes\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18_cubes\\server\\custom_addons', 'c:\\odoo18_cubes\\server\\odoo\\addons'] 
2025-09-24 20:31:10,158 112708 INFO ? odoo: database: openpg@localhost:5432 
2025-09-24 20:31:10,498 112708 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-09-24 20:31:10,520 112708 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-09-24 20:31:12,157 112708 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8087 
2025-09-24 20:31:14,607 112708 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-24 20:31:14,645 112708 INFO ? odoo.modules.loading: 1 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-09-24 20:31:14,676 112708 INFO ? odoo.modules.loading: loading 13 modules... 
2025-09-24 20:31:15,414 112708 INFO ? odoo.modules.loading: 13 modules loaded in 0.74s, 0 queries (+0 extra) 
2025-09-24 20:31:15,450 112708 INFO ? odoo.modules.loading: Modules loaded. 
2025-09-24 20:31:15,453 112708 INFO ? odoo.modules.registry: Registry loaded in 0.916s 
2025-09-24 20:31:15,453 112708 INFO kayan_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 20:31:15,511 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:15] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 20 0.059 0.917
2025-09-24 20:31:15,828 112708 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-09-24 20:31:16,130 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:16] "GET /odoo/apps HTTP/1.1" 200 - 78 0.111 0.780
2025-09-24 20:31:16,550 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:16] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.013 0.009
2025-09-24 20:31:16,617 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:16] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.014 0.008
2025-09-24 20:31:16,956 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:16] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.014
2025-09-24 20:31:20,219 112708 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 20:31:20,219 112708 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 20:31:20,221 112708 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 20:31:20,712 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:20] "GET /nati_arabic_font/static/src/fonts/Cairo/Cairo-Regular.woff HTTP/1.1" 304 - 0 0.000 3.803
2025-09-24 20:31:20,727 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:20] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 304 - 0 0.000 3.819
2025-09-24 20:31:20,755 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:20] "POST /web/action/load HTTP/1.1" 200 - 12 0.047 3.800
2025-09-24 20:31:21,127 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:21] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.018 0.015
2025-09-24 20:31:21,227 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:21] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.084 0.065
2025-09-24 20:31:21,627 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:21] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.012 0.056
2025-09-24 20:31:21,728 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:21] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.039 0.130
2025-09-24 20:31:21,826 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:21] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.017 0.250
2025-09-24 20:31:22,027 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /nati_arabic_font/static/src/fonts/Cairo/Cairo-Bold.woff HTTP/1.1" 304 - 0 0.000 0.035
2025-09-24 20:31:22,344 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /project/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.006
2025-09-24 20:31:22,344 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /purchase/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.010
2025-09-24 20:31:22,352 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /base/static/img/icons/mrp_workorder.png HTTP/1.1" 304 - 0 0.000 0.015
2025-09-24 20:31:22,354 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /hr_expense/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.011
2025-09-24 20:31:22,366 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /website_sale/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.022
2025-09-24 20:31:22,370 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /stock/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.019
2025-09-24 20:31:22,667 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /mass_mailing/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.004
2025-09-24 20:31:22,667 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /base/static/img/icons/web_studio.png HTTP/1.1" 304 - 0 0.000 0.004
2025-09-24 20:31:22,667 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /data_recycle/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:22,690 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /base/static/img/icons/industry_fsm.png HTTP/1.1" 304 - 0 0.000 0.023
2025-09-24 20:31:22,697 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /hr_holidays/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.015
2025-09-24 20:31:22,699 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /point_of_sale/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.009
2025-09-24 20:31:22,977 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /hr/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:22,990 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /base/static/img/icons/account_accountant.png HTTP/1.1" 304 - 0 0.000 0.002
2025-09-24 20:31:22,998 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:22] "GET /base/static/img/icons/timesheet_grid.png HTTP/1.1" 304 - 0 0.000 0.008
2025-09-24 20:31:23,015 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /mrp/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.011
2025-09-24 20:31:23,018 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /base/static/img/icons/knowledge.png HTTP/1.1" 304 - 0 0.000 0.012
2025-09-24 20:31:23,024 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /hr_recruitment/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.009
2025-09-24 20:31:23,298 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /website/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.009
2025-09-24 20:31:23,308 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /om_account_accountant/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.004
2025-09-24 20:31:23,308 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /sale_management/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:23,341 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /pos_restaurant/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.015
2025-09-24 20:31:23,348 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /account/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.018
2025-09-24 20:31:23,354 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /base/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.009
2025-09-24 20:31:23,642 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /crm/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.003
2025-09-24 20:31:23,666 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /maintenance/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.017
2025-09-24 20:31:23,684 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /marketing_card/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.018
2025-09-24 20:31:23,693 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /user_permissions_manager/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.015
2025-09-24 20:31:23,743 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.099 0.040
2025-09-24 20:31:23,812 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.016 0.159
2025-09-24 20:31:23,954 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /base/static/img/icons/sign.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:23,969 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /base/static/img/icons/helpdesk.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:23,997 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:23] "GET /base/static/img/icons/sale_subscription.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:24,004 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /base/static/img/icons/quality_control.png HTTP/1.1" 304 - 0 0.000 0.002
2025-09-24 20:31:24,059 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /website_slides/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:24,121 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /base/static/img/icons/planning.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:24,278 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /website_event/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:24,285 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /mail/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.002
2025-09-24 20:31:24,313 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /contacts/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.006
2025-09-24 20:31:24,321 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /base/static/img/icons/mrp_plm.png HTTP/1.1" 304 - 0 0.000 0.004
2025-09-24 20:31:24,378 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /calendar/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.002
2025-09-24 20:31:24,440 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /base/static/img/icons/social.png HTTP/1.1" 304 - 0 0.000 0.002
2025-09-24 20:31:24,596 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /base/static/img/icons/hr_appraisal.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:24,612 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "POST /web/action/load HTTP/1.1" 200 - 9 0.006 0.014
2025-09-24 20:31:24,631 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /fleet/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.004
2025-09-24 20:31:24,643 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /base/static/img/icons/marketing_automation.png HTTP/1.1" 304 - 0 0.000 0.007
2025-09-24 20:31:24,719 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "POST /web/dataset/call_kw/base.module.update/get_views HTTP/1.1" 200 - 12 0.010 0.021
2025-09-24 20:31:24,767 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "POST /web/dataset/call_kw/base.module.update/onchange HTTP/1.1" 200 - 2 0.000 0.016
2025-09-24 20:31:24,911 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /im_livechat/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.007
2025-09-24 20:31:24,944 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /base/static/img/icons/appointment.png HTTP/1.1" 304 - 0 0.000 0.008
2025-09-24 20:31:24,959 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /survey/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.015
2025-09-24 20:31:24,959 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:24] "GET /base/static/img/icons/web_mobile.png HTTP/1.1" 304 - 0 0.000 0.007
2025-09-24 20:31:25,041 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /repair/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.001
2025-09-24 20:31:25,070 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /hr_attendance/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:25,234 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /mass_mailing_sms/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.007
2025-09-24 20:31:25,262 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /base/static/img/icons/stock_barcode.png HTTP/1.1" 304 - 0 0.000 0.001
2025-09-24 20:31:25,277 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /project_todo/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.014
2025-09-24 20:31:25,278 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /hr_skills/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.016
2025-09-24 20:31:25,359 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /base/static/img/icons/voip.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:25,385 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /lunch/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.001
2025-09-24 20:31:25,543 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /website_hr_recruitment/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.003
2025-09-24 20:31:25,578 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /base/static/img/icons/sale_amazon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:25,593 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /hr_contract/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.015
2025-09-24 20:31:25,598 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /web/static/img/placeholder.png HTTP/1.1" 304 - 0 0.000 0.007
2025-09-24 20:31:25,676 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /accounting_pdf_reports/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-09-24 20:31:25,710 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:25] "GET /om_fiscal_year/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.003
2025-09-24 20:31:26,144 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:26] "POST /web/dataset/call_kw/base.module.update/web_save HTTP/1.1" 200 - 5 0.001 0.015
2025-09-24 20:31:26,400 112708 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user admin #2 via 127.0.0.1 
2025-09-24 20:31:27,239 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:27] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.023
2025-09-24 20:31:28,511 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:28] "POST /web/dataset/call_button/base.module.update/update_module HTTP/1.1" 200 - 1573 1.297 0.817
2025-09-24 20:31:40,629 112708 INFO ? werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:40] "GET /nati_arabic_font/static/src/fonts/Cairo/Cairo-BoldItalic.woff HTTP/1.1" 304 - 0 0.000 0.015
2025-09-24 20:31:42,189 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:42] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.102 0.041
2025-09-24 20:31:42,528 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:42] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.008 0.012
2025-09-24 20:31:43,832 112708 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 20:31:43,838 112708 INFO kayan_2 odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-09-24 20:31:43,838 112708 INFO kayan_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Odoo 18 Full Accounting Kit for Community'] to user admin #2 via 127.0.0.1 
2025-09-24 20:31:43,979 112708 WARNING kayan_2 odoo.http: Unable to install module "base_accounting_kit" because an external dependency is not met: External dependency qifparse not installed: No package metadata was found for qifparse 
2025-09-24 20:31:43,979 112708 INFO kayan_2 werkzeug: 127.0.0.1 - - [24/Sep/2025 20:31:43] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 59 0.032 0.122
2025-09-24 20:33:12,736 131124 INFO ? odoo: Odoo version 18.0-******** 
2025-09-24 20:33:12,736 131124 INFO ? odoo: Using configuration file at C:\odoo18_cubes\server\odoo.conf 
2025-09-24 20:33:12,736 131124 INFO ? odoo: addons paths: ['C:\\odoo18_cubes\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18_cubes\\server\\custom_addons', 'c:\\odoo18_cubes\\server\\odoo\\addons'] 
2025-09-24 20:33:12,736 131124 INFO ? odoo: database: openpg@localhost:5432 
2025-09-24 20:33:12,895 131124 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-09-24 20:33:12,925 131124 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
