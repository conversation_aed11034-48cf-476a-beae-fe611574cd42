<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--    Report Template for General <PERSON>-->
    <template id="general_ledger">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="data_report_margin_top" t-value="12"/>
                <t t-set="data_report_header_spacing" t-value="9"/>
                <t t-set="data_report_dpi" t-value="110"/>
                <div class="page">
                    <h3>
                        <center>
                            <b>
                                <span t-esc="report_name"/>
                            </b>
                        </center>
                    </h3>
                    <br/>
                    <br/>
                    <div class="filters">
                        <table class="table table-sm table-reports">
                            <thead class="filter_table"
                                   style="background:#808080;">
                                <tr>
                                    <th>Date Range</th>
                                    <th>Journal</th>
                                    <th>Analytic</th>
                                    <th>Options</th>
                                </tr>
                            </thead>
                            <tbody style="font-size:11px;font-weight:100;">
                                <tr>
                                    <th>
                                        <t t-if="filters['start_date']"
                                           t-out="filters['start_date']"/>
                                        <t t-if="filters['end_date']">
                                            to
                                            <t t-out="filters['end_date']"/>
                                        </t>
                                    </th>
                                    <th>
                                        <t t-set="first_journal" t-value="True"/>
                                        <t t-foreach="filters['journal']" t-as="selected_journal">
                                            <t t-if="not first_journal">,</t>
                                            <t t-out="selected_journal"/>
                                            <t t-set="first_journal" t-value="False"/>
                                        </t>
                                    </th>
                                    <th>
                                        <t t-set="first_account" t-value="True"/>
                                        <t t-foreach="filters['analytic']" t-as="selected_analytic_account">
                                            <t t-if="not first_account">,</t>
                                            <t t-out="selected_analytic_account"/>
                                            <t t-set="first_account" t-value="False"/>
                                        </t>
                                    </th>
                                    <th>
                                        <t t-if="filters['options']">
                                            Posted ,
                                        </t>
                                        <t t-else="">
                                            Posted
                                        </t>
                                        <t t-set="first_option" t-value="True"/>
                                        <t t-foreach="filters['options']" t-as="selected_options">
                                            <t t-if="not first_option">,</t>
                                            <t t-out="selected_options"/>
                                            <t t-set="first_option" t-value="False"/>
                                        </t>
                                    </th>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <br/>
                    <br/>
                    <br/>
                    <t t-if="account">
<!--                        <t t-foreach="account_data.account_totals"-->
<!--                           t-as="accounts"-->
<!--                           t-key="accounts_index">-->
                            <table class="table table-sm table-reports">
                                <thead style="background:#808080;">
                                    <tr>
                                        <th style="width:10%" colspan="6"/>
                                        <th style="width:10%">Date</th>
                                        <th style="width:10%">Communication
                                        </th>
                                        <th style="width:10%">Partner</th>
                                        <th style="width:10%">Debit</th>
                                        <th style="width:10%">Credit</th>
                                        <th style="width:10%">Balance</th>
                                    </tr>
                                </thead>
                                <tbody style="font-size:11px;font-weight:100;">
                                    <t t-foreach="account"
                           t-as="account"
                           t-key="accounts_index">
                                    <tr class="border-bottom"
                                        style="background:#D3D3D3;">
                                        <th colspan="6">
                                            <div class="ms-3">
                                                <span class="fw-bolder">
                                                    <t t-if="accounts != 'false'">
                                                        <strong>
                                                            <b>
                                                                <t t-esc="account"/>
                                                            </b>
                                                        </strong>
                                                    </t>
                                                    <t t-else="">
                                                        <span>Unknown
                                                            Account
                                                        </span>
                                                    </t>
                                                </span>
                                            </div>
                                        </th>
                                        <th style="width:10% border:0px solid transparent;"/>
                                        <th style="width:10% border:0px solid transparent;"/>
                                        <th style="width:10% border:0px solid transparent;"/>
                                        <th style="width:10% border:0px solid transparent;">
                                            <strong>
                                                <span>
                                                    <t t-if="total[account]['total_debit']"
                                                       t-esc="total[account]['currency_id']"/>
                                                    <t t-if="total[account]['total_debit']"
                                                       t-esc="total[account]['total_debit_display']"/>
                                                </span>
                                            </strong>
                                        </th>
                                        <th style="width:10% border:0px solid transparent;">
                                            <strong>
                                                <span>
                                                    <t t-if="total[account]['total_credit']"
                                                       t-esc="total[account]['currency_id']"/>
                                                    <t t-if="total[account]['total_credit']"
                                                       t-esc="total[account]['total_credit_display']"/>
                                                </span>
                                            </strong>
                                        </th>
                                        <th style="width:10% border:0px solid transparent;border-right: thin solid #dee2e6;">
                                            <strong>
                                                <span class="fw-bolder">
                                                    <t t-esc="total[account]['currency_id']"/>
                                                    <t t-esc="total[account]['total_debit'] - total[account]['total_credit']"/>
                                                </span>
                                            </strong>
                                        </th>
                                    </tr>
                                    <t t-foreach="account_data[account]"
                                       t-as="valuelist"
                                       t-key="valuelist_index">
                                        <tr class="border-bottom">
                                            <th colspan="6" style="width:10%">
                                                <span>
                                                    <t t-esc="valuelist[0]['move_name']"/>
                                                </span>
                                            </th>
                                            <th style="width:10%">
                                                <span>
                                                    <t t-esc="valuelist[0]['date']"/>
                                                </span>
                                            </th>
                                            <th style="width:10%">
                                                <span>
                                                    <t t-esc="valuelist[0]['name']"/>
                                                </span>
                                            </th>
                                            <th style="width:10%">
                                                <span>
                                                    <t t-if="valuelist[0]['partner_id']"
                                                       t-esc="valuelist[0]['partner_id'][1]"/>
                                                </span>
                                            </th>
                                            <th style="width:10%">
                                                <span>
                                                    <t t-if="valuelist[0]['debit']"
                                                       t-esc="total[account]['currency_id']"/>
                                                    <t t-if="valuelist[0]['debit']"
                                                       t-esc="valuelist[0]['debit']"/>
                                                </span>
                                            </th>
                                            <th style="width:10%">
                                                <span>
                                                    <t t-if="valuelist[0]['credit']"
                                                       t-esc="total[account]['currency_id']"/>
                                                    <t t-if="valuelist[0]['credit']"
                                                       t-esc="valuelist[0]['credit']"/>
                                                </span>
                                            </th>
                                            <th style="width:10%"/>
                                        </tr>
                                    </t>
                                    </t>
                                </tbody>
                            </table>
<!--                        </t>-->
                    </t>
                    <table class="table table-sm table-reports">
                        <tbody>
                            <tr>
                                <th style="width:45%;">Total</th>
                                <th style="width:10%">
                                    <t t-out="grand_total['currency']"/>
                                    <t t-if="grand_total['total_debit']">
                                    <t t-out="grand_total['total_debit_display']"/>
                                    </t>
                                </th>
                                <th style="width:10%">
                                    <t t-out="grand_total['currency']"/>
                                    <t t-out="grand_total['total_credit_display']"/>
                                </th>
                                <th style="width:10%">
                                    <t t-out="grand_total['currency']"/>
                                    <t t-if="grand_total['total_debit'] and grand_total['total_credit']">
                                    <t t-out="float(grand_total['total_debit']) - float(grand_total['total_credit'])"/>
                                    </t>
                                </th>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </t>
        </t>
    </template>
</odoo>
