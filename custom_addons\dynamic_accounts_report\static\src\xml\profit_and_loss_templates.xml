<?xml version="1.0" encoding="utf-8"?>
<templates>
    <t t-name="dfr_template_new" owl="1">
        <div class="container">
            <div style="border-bottom: 1px solid #d8dadd;">
                <div class="filter_view_dfr pt-3 pb-5">
                    <div class="">
                        <h2 class="text-uppercase" style="padding:10px">
                            <t t-esc="state.title"/>
                        </h2>
                    </div>
                    <div class="sub_container_right d-flex mx-2">
                        <t t-if="state.filter_data">
                            <div class="report_print">
                                <button type="button"
                                        class="btn btn-primary btn-report-print mr-2"
                                        t-on-click="print_pdf"
                                        style="margin-right: 8px;">
                                    Print (PDF)
                                </button>
                                <button type="button"
                                        class="btn btn-primary btn-report-print"
                                        t-on-click="print_xlsx"
                                        style="position: relative;">
                                    Export (XLSX)
                                </button>
                            </div>
                            <div class="filter d-flex ms-auto"
                                 style="gap: 1.5rem;">
                                <div class="time_range" style="">
                                    <a type="button" class="dropdown-toggle"
                                       data-bs-toggle="dropdown">
                                        <span class="fa fa-calendar"
                                              title="Dates"
                                              role="img"
                                              aria-label="Dates"/>
                                        Date Range
                                    </a>
                                    <div class="dropdown-menu" role="menu">
                                        <div class="list-group">
                                            <button class="report-filter-button"
                                                    t-att-data-value="'month'"
                                                    type="button"
                                                    t-on-click="apply_date">
                                                This Month
                                            </button>
                                            <button class="report-filter-button"
                                                    t-att-data-value="'quarter'"
                                                    type="button"
                                                    t-on-click="apply_date">
                                                This Quarter
                                            </button>
                                            <button class="report-filter-button"
                                                    t-att-data-value="'year'"
                                                    type="button"
                                                    t-on-click="apply_date">
                                                This Year
                                            </button>
                                            <div role="separator"
                                                 class="dropdown-divider"/>
                                            <button class="report-filter-button"
                                                    t-att-data-value="'last-month'"
                                                    type="button"
                                                    t-on-click="apply_date">
                                                Last month
                                            </button>
                                            <button class="report-filter-button"
                                                    t-att-data-value="'last-quarter'"
                                                    type="button"
                                                    t-on-click="apply_date">
                                                Last quarter
                                            </button>
                                            <button class="report-filter-button"
                                                    t-att-data-value="'last-year'"
                                                    type="button"
                                                    t-on-click="apply_date">
                                                Last year
                                            </button>
                                            <div role="separator"
                                                 class="dropdown-divider"/>
                                            <label class="" for="date_from">
                                                Start
                                                Date :
                                            </label>
                                            <div class="input-group date"
                                                 t-ref="date_from"
                                                 data-target-input="nearest">

                                                <input type="date"
                                                       id="start_date"
                                                       t-on-change="apply_date"
                                                       style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                       name="start_date"/>
                                            </div>

                                            <label class="" for="date_to">End
                                                Date
                                                :
                                            </label>
                                            <div class="input-group date"
                                                 t-ref="date_to"
                                                 data-target-input="nearest">
                                                <input type="date"
                                                       id="end_date"
                                                       t-on-change="apply_date"
                                                       style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                       name="end_date"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="comparison_range" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <span class="fa fa-signal"
                                          title="Comparison"
                                          role="img"
                                          aria-label="Comparison"/>
                                    Comparison
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <div class="list-group">
                                        <button class="report-filter-button"
                                                t-att-data-value="'no comparison'"
                                                type="button"
                                                t-on-click="apply_comparison">
                                            No Comparison
                                        </button>
                                        <div role="separator"
                                             class="dropdown-divider"/>
                                        <label class="" for="periods"
                                               style="display: flex;padding: 4px 20px;color: #000;">
                                            Previous Period
                                            <br/>
                                            Number of Periods:
                                        </label>
                                        <div class="input-group comparison"
                                             data-target-input="nearest">

                                            <input type="number"
                                                   t-ref="periods"
                                                   min="1"
                                                   t-on-input="onPeriodChange"
                                                   style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                   name="previous_period"/>
                                        </div>
                                        <button class="btn btn-primary"
                                                type="button"
                                                style="margin-left: 19px;margin-top: 4px;"
                                                t-on-click="applyComparisonPeriod">
                                            Apply
                                        </button>
                                        <div role="separator"
                                             class="dropdown-divider"/>
                                        <label class="" for="period_year"
                                               style="display: flex;padding: 4px 20px;color: #000;">
                                            Same Period Last Year
                                            <br/>
                                            Number of Periods:
                                        </label>
                                        <div class="input-group comparison"
                                             data-target-input="nearest">
                                            <input type="number"
                                                   t-ref="period_year"
                                                   t-on-input="onPeriodYearChange"
                                                   min="1"
                                                   style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                   name="period_year"/>
                                        </div>
                                        <button class="btn btn-primary"
                                                type="button"
                                                style="margin-left: 19px;margin-top: 4px;"
                                                t-on-click="applyComparisonYear">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="journals_filter dropdown" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <span class="fa fa-book"/>
                                    Journals:
                                    <span class="code"/>
                                </a>
                                <div class="dropdown-menu journals"
                                     name="states[]">
                                    <div role="separator"
                                         class="dropdown-divider"/>
                                    <t t-foreach="state.filter_data['journal']"
                                       t-as="journal" t-key="journal_index">
                                        <span class="dropdown-item"
                                              role="menuitem"
                                              t-on-click="apply_journal">
                                            <span t-esc="journal.id"
                                                  class="d-none"/>
                                            <t t-esc="journal.name"/>
                                        </span>
                                    </t>
                                </div>
                            </div>
                            <div class="accounts_filter">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown" style="display: flex;text-wrap: wrap;
                                                                    flex-direction: row;
                                                                    align-items: center;
                                                                    justify-content: flex-start;
                                                                    min-width: 105px;">
                                    <span class="fa fa-book"/>
                                    Accounts:
                                    <span class="account"/>
                                </a>
                                <div class="dropdown-menu journals"
                                     name="states[]">
                                    <div role="separator"
                                         class="dropdown-divider"/>
                                    <t t-foreach="state.filter_data['account']"
                                       t-as="account" t-key="account_index">
                                        <span class="dropdown-item"
                                              role="menuitem"
                                              t-on-click="apply_account">
                                            <span t-esc="account.id"
                                                  class="d-none"/>
                                            <t t-esc="account.name"/>
                                        </span>
                                    </t>
                                </div>
                            </div>
                            <div class="analytic_filter" style="max-width:17%;">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown" style="display: flex;
                                                            text-wrap: wrap;
                                                            flex-direction: row;
                                                            align-items: center;
                                                            justify-content: flex-start;
                                                            min-width: 105px;">
                                    <span class="fa fa-folder-open"/>
                                    Analytic Accounts:
                                    <span class="analytic"/>
                                </a>
                                <div class="dropdown-menu analytic_accounts"
                                     name="states[]">
                                    <div role="separator"
                                         class="dropdown-divider"/>
                                    <t t-foreach="state.filter_data['analytic']"
                                       t-as="analytic" t-key="analytic_index">
                                        <span class="dropdown-item"
                                              role="menuitem"
                                              t-on-click="apply_analytic_accounts">
                                            <span t-esc="analytic.id"
                                                  class="d-none"/>
                                            <t t-esc="analytic.name"/>
                                        </span>
                                    </t>
                                </div>
                            </div>
                            <div class="search_Target_move" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <span class="fa fa-filter"/>
                                    Target Move:
                                    <span class="target"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <div class="list-group">
                                        <button class="report-filter-button"
                                                t-attf-value="posted"
                                                t-ref="posted"
                                                type="button"
                                                t-on-click="apply_entries">
                                            Posted Entries
                                        </button>
                                        <button class="report-filter-button"
                                                t-attf-value="draft"
                                                t-ref="draft"
                                                type="button"
                                                t-on-click="apply_entries">
                                            Include Draft Entries
                                        </button>
                                        <button class="report-filter-button"
                                                type="button"
                                                t-ref="unfoldButton"
                                                t-on-click="unfoldAll">
                                            Unfold All
                                        </button>

                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
            <br/>
            <div>
                <div class="table_view_dfr" style="height: 650px; overflow-y: scroll;">
                    <div>
                        <div class="table_main_view">
                            <table cellspacing="0" width="100%">
                                <thead>
                                    <tr class="o_heading">
                                        <th colspan="6"/>
                                        <t t-if="state.year">
                                            <t t-foreach="state.year"
                                               t-as="periodData"
                                               t-key="periodData_index">
                                                <th class="text-end">
                                                    <t t-esc="periodData"/>
                                                </th>
                                            </t>
                                        </t>
                                    </tr>
                                    <tr class="o_heading">
                                        <th colspan="6"/>
                                        <t t-if="state.year">
                                            <t t-foreach="state.year"
                                               t-as="periodData"
                                               t-key="periodData_index">
                                                <th class="text-end">Balance
                                                </th>
                                            </t>
                                        </t>
                                    </tr>
                                </thead>
                                <tbody t-ref="tbody">
                                    <t t-if="state.datas">
                                        <tr class="gl-line">
                                            <th colspan="6">
                                                <span class="fw-bolder">
                                                    Net Profit
                                                </span>
                                            </th>
                                            <t t-foreach="state.datas"
                                               t-as="total"
                                               t-key="total_index">
                                                <th class="text-end">
                                                    <span class="fw-bolder">
                                                        <t t-if="total.total"
                                                           t-esc="total.total.toFixed(2)"/>
                                                    </span>
                                                </th>
                                            </t>
                                        </tr>
                                        <tr class="border-bottom border-dark">
                                            <th colspan="6">
                                                <span class="fw-bolder ms-2">
                                                    Income
                                                </span>
                                            </th>
                                            <t t-foreach="state.datas"
                                               t-as="total_income"
                                               t-key="total_income_index">
                                                <th class="text-end">
                                                    <span class="fw-bolder">
                                                        <t t-esc="total_income.total_income"/>
                                                    </span>
                                                </th>
                                            </t>
                                        </tr>
                                        <tr class="border-bottom border-gainsboro">
                                            <th colspan="6">
                                                <span class="fw-bolder ms-3">
                                                    Gross Profit
                                                </span>
                                            </th>
                                            <t t-foreach="state.datas"
                                                   t-as="value"
                                                   t-key="value_index">
                                                    <th class="text-end">
                                                        <span>
                                                            <t t-esc="sumGrossProfit(value.expense_direct_cost[1], value.income[1]).toFixed(2)"/>
                                                        </span>
                                                    </th>
                                                </t>
                                        </tr>
                                        <t t-foreach="state.datas" t-as="value"
                                           t-key="value_index">
                                            <t t-if="value.income[1] != '0.00'">
                                                <t t-set="income" t-value="1"/>
                                            </t>
                                        </t>
                                        <t t-if="income == 1">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="6">
                                                    <div data-bs-toggle="collapse"
                                                         href="#collapseIncome"
                                                         aria-expanded="false"
                                                         aria-controls="collapseIncome"
                                                         class="ms-3 collapsed">
                                                        <a class="btn header">
                                                            <span class="toggle-icon">
                                                                <i class="fa fa-caret-down"/>
                                                            </span>
                                                            <span class="ms-3">
                                                                Operating
                                                                Income
                                                            </span>
                                                        </a>
                                                    </div>
                                                </th>
                                                <t t-foreach="state.datas"
                                                   t-as="value"
                                                   t-key="value_index">
                                                    <th class="text-end">
                                                        <span>
                                                            <t t-esc="value.income[1]"/>
                                                        </span>
                                                    </th>
                                                </t>
                                            </tr>
                                            <t t-foreach="state.datas"
                                               t-as="value"
                                               t-key="value_index">
                                                <t t-if="value_index == 0">
                                                    <t t-foreach="value.income[0]"
                                                       t-as="income"
                                                       t-key="income_index">
                                                        <t t-set="income_name"
                                                           t-value="income.name"/>
                                                        <t t-set="account_value"
                                                           t-value="0"/>
                                                        <t t-foreach="state.datas"
                                                           t-as="values"
                                                           t-key="values_index">
                                                            <t t-foreach="values.income[0]"
                                                               t-as="income"
                                                               t-key="income_index">
                                                                <t t-if="income_name == income.name">
                                                                    <t t-if="income.amount != '0.00'">
                                                                        <t t-set="account_value"
                                                                           t-value="1"/>
                                                                    </t>
                                                                </t>
                                                            </t>
                                                        </t>
                                                        <t t-if="account_value == 1">
                                                            <tr class="border-bottom border-gainsboro collapse"
                                                                id="collapseIncome">
                                                                <th colspan="6">
                                                                    <span class="dropdown">
                                                                        <a class="dropdown-toggle"
                                                                           data-bs-toggle="dropdown">
                                                                            <span class="ms-5">
                                                                                <t t-esc="income.name"/>
                                                                            </span>
                                                                        </a>
                                                                        <span class="dropdown-menu">
                                                                            <a role="menuitem"
                                                                               t-on-click="show_gl"
                                                                               class="show_gl">
                                                                                General
                                                                                Ledger
                                                                            </a>
                                                                        </span>
                                                                    </span>
                                                                </th>
                                                                <t t-foreach="state.datas"
                                                                   t-as="values"
                                                                   t-key="values_index">
                                                                    <t t-foreach="values.income[0]"
                                                                       t-as="income"
                                                                       t-key="income_index">
                                                                        <t t-if="income_name == income.name">
                                                                            <th class="text-end">
                                                                                <span>
                                                                                    <t t-esc="income.amount"/>
                                                                                </span>
                                                                            </th>
                                                                        </t>
                                                                    </t>
                                                                </t>
                                                            </tr>
                                                        </t>
                                                    </t>
                                                </t>
                                            </t>
                                        </t>
                                        <t t-else="">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="9">
                                                    <span class="ms-4">
                                                        Operating
                                                        Income
                                                    </span>
                                                </th>
                                            </tr>
                                        </t>
                                        <t t-foreach="state.datas" t-as="value"
                                           t-key="value_index">
                                            <t t-if="value.expense_direct_cost[1] != '0.00'">
                                                <t t-set="expense_direct_cost"
                                                   t-value="1"/>
                                            </t>
                                        </t>
                                        <t t-if="expense_direct_cost  == 1">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="6">
                                                    <div data-bs-toggle="collapse"
                                                         href="#collapseRevenue"
                                                         aria-expanded="false"
                                                         aria-controls="collapseRevenue"
                                                         class="ms-3 collapsed">
                                                        <a class="btn header">
                                                            <span class="toggle-icon">
                                                                <i class="fa fa-caret-down"/>
                                                            </span>
                                                            <span class="ms-3">
                                                                Cost
                                                                of
                                                                Revenue
                                                            </span>
                                                        </a>
                                                    </div>
                                                </th>
                                                <t t-foreach="state.datas"
                                                   t-as="value"
                                                   t-key="value_index">
                                                    <th class="text-end">
                                                        <span>
                                                            <t t-esc="value.expense_direct_cost[1]"/>
                                                        </span>
                                                    </th>
                                                </t>
                                            </tr>
                                            <t t-foreach="state.datas"
                                               t-as="value"
                                               t-key="value_index">
                                                <t t-if="value_index == 0">
                                                    <t t-foreach="value.expense_direct_cost[0]"
                                                       t-as="income"
                                                       t-key="income_index">
                                                        <t t-set="income_name"
                                                           t-value="income.name"/>
                                                        <t t-set="account_value"
                                                           t-value="0"/>
                                                        <t t-foreach="state.datas"
                                                           t-as="values"
                                                           t-key="values_index">
                                                            <t t-foreach="values.expense_direct_cost[0]"
                                                               t-as="income"
                                                               t-key="income_index">
                                                                <t t-if="income_name == income.name">
                                                                    <t t-if="income.amount != '0.00'">
                                                                        <t t-set="account_value"
                                                                           t-value="1"/>
                                                                    </t>
                                                                </t>
                                                            </t>
                                                        </t>
                                                        <t t-if="account_value == 1">
                                                            <tr class="border-bottom border-gainsboro collapse"
                                                                id="collapseRevenue">
                                                                <th colspan="6">
                                                                    <span class="dropdown">
                                                                        <a class="dropdown-toggle"
                                                                           data-bs-toggle="dropdown">
                                                                            <span class="ms-5">
                                                                                <t t-esc="income.name"/>
                                                                            </span>
                                                                        </a>
                                                                        <span class="dropdown-menu">
                                                                            <a role="menuitem"
                                                                               t-on-click="show_gl"
                                                                               class="show_gl">
                                                                                General
                                                                                Ledger
                                                                            </a>
                                                                        </span>
                                                                    </span>
                                                                </th>
                                                                <t t-foreach="state.datas"
                                                                   t-as="values"
                                                                   t-key="values_index">
                                                                    <t t-foreach="values.expense_direct_cost[0]"
                                                                       t-as="income"
                                                                       t-key="income_index">
                                                                        <t t-if="income_name == income.name">
                                                                            <th class="text-end">
                                                                                <span>
                                                                                    <t t-esc="income.amount"/>
                                                                                </span>
                                                                            </th>
                                                                        </t>
                                                                    </t>
                                                                </t>
                                                            </tr>
                                                        </t>
                                                    </t>
                                                </t>
                                            </t>
                                        </t>
                                        <t t-else="">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="9">
                                                    <span class="ms-4">Cost of
                                                        Revenue
                                                    </span>
                                                </th>
                                            </tr>
                                        </t>
                                        <t t-foreach="state.datas" t-as="value"
                                           t-key="value_index">
                                            <t t-if="value.income_other[1] != '0.00'">
                                                <t t-set="income_other"
                                                   t-value="1"/>
                                            </t>
                                        </t>
                                        <t t-if="income_other  == 1">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="6">
                                                    <div data-bs-toggle="collapse"
                                                         href="#collapseOtherIncome"
                                                         aria-expanded="false"
                                                         aria-controls="collapseOtherIncome"
                                                         class="ms-1 collapsed">
                                                        <a class="btn header">
                                                            <span class="toggle-icon">
                                                                <i class="fa fa-caret-down"/>
                                                            </span>
                                                            <span class="ms-3">
                                                                Other Income
                                                            </span>
                                                        </a>
                                                    </div>
                                                </th>
                                                <t t-foreach="state.datas"
                                                   t-as="value"
                                                   t-key="value_index">
                                                    <th class="text-end">
                                                        <span>
                                                            <t t-esc="value.income_other[1]"/>
                                                        </span>
                                                    </th>
                                                </t>
                                            </tr>
                                            <t t-foreach="state.datas"
                                               t-as="value"
                                               t-key="value_index">
                                                <t t-if="value_index == 0">
                                                    <t t-foreach="value.income_other[0]"
                                                       t-as="income"
                                                       t-key="income_index">
                                                        <t t-set="income_name"
                                                           t-value="income.name"/>
                                                        <t t-set="account_value"
                                                           t-value="0"/>
                                                        <t t-foreach="state.datas"
                                                           t-as="values"
                                                           t-key="values_index">
                                                            <t t-foreach="values.income_other[0]"
                                                               t-as="income"
                                                               t-key="income_index">
                                                                <t t-if="income_name == income.name">
                                                                    <t t-if="income.amount != '0.00'">
                                                                        <t t-set="account_value"
                                                                           t-value="1"/>
                                                                    </t>
                                                                </t>
                                                            </t>
                                                        </t>
                                                        <t t-if="account_value == 1">
                                                            <tr class="border-bottom border-gainsboro collapse"
                                                                id="collapseOtherIncome">
                                                                <th colspan="6">
                                                                    <span class="dropdown">
                                                                        <a class="dropdown-toggle"
                                                                           data-bs-toggle="dropdown">
                                                                            <span class="ms-4">
                                                                                <t t-esc="income.name"/>
                                                                            </span>
                                                                        </a>
                                                                        <span class="dropdown-menu">
                                                                            <a role="menuitem"
                                                                               t-on-click="show_gl"
                                                                               class="show_gl">
                                                                                General
                                                                                Ledger
                                                                            </a>
                                                                        </span>
                                                                    </span>
                                                                </th>
                                                                <t t-foreach="state.datas"
                                                                   t-as="values"
                                                                   t-key="values_index">
                                                                    <t t-foreach="values.income_other[0]"
                                                                       t-as="income"
                                                                       t-key="income_index">
                                                                        <t t-if="income_name == income.name">
                                                                            <th class="text-end">
                                                                                <span>
                                                                                    <t t-esc="income.amount"/>
                                                                                </span>
                                                                            </th>
                                                                        </t>
                                                                    </t>
                                                                </t>
                                                            </tr>
                                                        </t>
                                                    </t>
                                                </t>
                                            </t>
                                        </t>
                                        <t t-else="">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="9">
                                                    <span class="ms-3">Other
                                                        Income
                                                    </span>
                                                </th>
                                            </tr>
                                        </t>
                                        <tr class="border-bottom border-gainsboro">
                                            <th colspan="6">
                                                <span class="fw-bolder ms-2">
                                                    Total Income
                                                </span>
                                            </th>
                                            <t t-foreach="state.datas"
                                               t-as="total_income"
                                               t-key="total_income_index">
                                                <th class="text-end">
                                                    <span class="fw-bolder">
                                                        <t t-esc="total_income.total_income"/>
                                                    </span>
                                                </th>
                                            </t>
                                        </tr>
                                        <tr class="border-bottom border-dark">
                                            <th colspan="6">
                                                <span class="fw-bolder ms-1">
                                                    Expenses
                                                </span>
                                            </th>
                                            <t t-foreach="state.datas"
                                               t-as="total_expense"
                                               t-key="total_expense_index">
                                                <th class="text-end">
                                                    <span class="fw-bolder">
                                                        <t t-esc="total_expense.total_expense"/>
                                                    </span>
                                                </th>
                                            </t>
                                        </tr>
                                        <t t-foreach="state.datas" t-as="value"
                                           t-key="value_index">
                                            <t t-if="value.expense[1] != '0.00'">
                                                <t t-set="expense"
                                                   t-value="1"/>
                                            </t>
                                        </t>
                                        <t t-if="expense  == 1">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="6">
                                                    <div data-bs-toggle="collapse"
                                                         href="#collapseExpenses"
                                                         aria-expanded="false"
                                                         aria-controls="collapseExpenses"
                                                         class="ms-2 collapsed">
                                                        <a class="btn header">
                                                            <span class="toggle-icon">
                                                                <i class="fa fa-caret-down"/>
                                                            </span>
                                                            <span class="ms-3">
                                                                Expenses
                                                            </span>
                                                        </a>
                                                    </div>
                                                </th>
                                                <t t-foreach="state.datas"
                                                   t-as="value"
                                                   t-key="value_index">
                                                    <th class="text-end">
                                                        <span>
                                                            <t t-esc="value.expense[1]"/>
                                                        </span>
                                                    </th>
                                                </t>
                                            </tr>
                                            <t t-foreach="state.datas"
                                               t-as="value"
                                               t-key="value_index">
                                                <t t-if="value_index == 0">
                                                    <t t-foreach="value.expense[0]"
                                                       t-as="income"
                                                       t-key="income_index">
                                                        <t t-set="income_name"
                                                           t-value="income.name"/>
                                                        <t t-set="account_value"
                                                           t-value="0"/>
                                                        <t t-foreach="state.datas"
                                                           t-as="values"
                                                           t-key="values_index">
                                                            <t t-foreach="values.expense[0]"
                                                               t-as="income"
                                                               t-key="income_index">
                                                                <t t-if="income_name == income.name">
                                                                    <t t-if="income.amount != '0.00'">
                                                                        <t t-set="account_value"
                                                                           t-value="1"/>
                                                                    </t>
                                                                </t>
                                                            </t>
                                                        </t>
                                                        <t t-if="account_value == 1">
                                                            <tr class="border-bottom border-gainsboro collapse"
                                                                id="collapseExpenses">
                                                                <th colspan="6">
                                                                    <span class="dropdown">
                                                                        <a class="dropdown-toggle"
                                                                           data-bs-toggle="dropdown">
                                                                            <span class="ms-5">
                                                                                <t t-esc="income.name"/>
                                                                            </span>
                                                                        </a>
                                                                        <span class="dropdown-menu">
                                                                            <a role="menuitem"
                                                                               t-on-click="show_gl"
                                                                               class="show_gl">
                                                                                General
                                                                                Ledger
                                                                            </a>
                                                                        </span>
                                                                    </span>
                                                                </th>
                                                                <t t-foreach="state.datas"
                                                                   t-as="values"
                                                                   t-key="values_index">
                                                                    <t t-foreach="values.expense[0]"
                                                                       t-as="income"
                                                                       t-key="income_index">
                                                                        <t t-if="income_name == income.name">
                                                                            <th class="text-end">
                                                                                <span>
                                                                                    <t t-esc="income.amount"/>
                                                                                </span>
                                                                            </th>
                                                                        </t>
                                                                    </t>
                                                                </t>
                                                            </tr>
                                                        </t>
                                                    </t>
                                                </t>
                                            </t>
                                        </t>
                                        <t t-else="">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="9">
                                                    <span class="ms-2">Expenses
                                                    </span>
                                                </th>
                                            </tr>
                                        </t>
                                        <t t-foreach="state.datas" t-as="value"
                                           t-key="value_index">
                                            <t t-if="value.expense_depreciation[1] != '0.00'">
                                                <t t-set="expense_depreciation"
                                                   t-value="1"/>
                                            </t>
                                        </t>
                                        <t t-if="expense_depreciation  == 1">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="6">
                                                    <div data-bs-toggle="collapse"
                                                         href="#collapseDepreciation"
                                                         aria-expanded="false"
                                                         aria-controls="collapseDepreciation"
                                                         class="ms-2 collapsed">
                                                        <a class="btn header">
                                                            <span class="toggle-icon">
                                                                <i class="fa fa-caret-down"/>
                                                            </span>
                                                            <span class="ms-3">
                                                                Depreciation
                                                            </span>
                                                        </a>
                                                    </div>
                                                </th>
                                                <t t-foreach="state.datas"
                                                   t-as="value"
                                                   t-key="value_index">
                                                    <th class="text-end">
                                                        <span>
                                                            <t t-esc="value.expense_depreciation[1]"/>
                                                        </span>
                                                    </th>
                                                </t>
                                            </tr>
                                            <t t-foreach="state.datas"
                                               t-as="value"
                                               t-key="value_index">
                                                <t t-if="value_index == 0">
                                                    <t t-foreach="value.expense_depreciation[0]"
                                                       t-as="income"
                                                       t-key="income_index">
                                                        <t t-set="income_name"
                                                           t-value="income.name"/>
                                                        <t t-set="account_value"
                                                           t-value="0"/>
                                                        <t t-foreach="state.datas"
                                                           t-as="values"
                                                           t-key="values_index">
                                                            <t t-foreach="values.expense_depreciation[0]"
                                                               t-as="income"
                                                               t-key="income_index">
                                                                <t t-if="income_name == income.name">
                                                                    <t t-if="income.amount != '0.00'">
                                                                        <t t-set="account_value"
                                                                           t-value="1"/>
                                                                    </t>
                                                                </t>
                                                            </t>
                                                        </t>
                                                        <t t-if="account_value == 1">
                                                            <tr class="border-bottom border-gainsboro collapse"
                                                                id="collapseDepreciation">
                                                                <th colspan="6">
                                                                    <span class="dropdown">
                                                                        <a class="dropdown-toggle"
                                                                           data-bs-toggle="dropdown">
                                                                            <span class="ms-5">
                                                                                <t t-esc="income.name"/>
                                                                            </span>
                                                                        </a>
                                                                        <span class="dropdown-menu">
                                                                            <a role="menuitem"
                                                                               t-on-click="show_gl"
                                                                               class="show_gl">
                                                                                General
                                                                                Ledger
                                                                            </a>
                                                                        </span>
                                                                    </span>
                                                                </th>
                                                                <t t-foreach="state.datas"
                                                                   t-as="values"
                                                                   t-key="values_index">
                                                                    <t t-foreach="values.expense_depreciation[0]"
                                                                       t-as="income"
                                                                       t-key="income_index">
                                                                        <t t-if="income_name == income.name">
                                                                            <th class="text-end">
                                                                                <span>
                                                                                    <t t-esc="income.amount"/>
                                                                                </span>
                                                                            </th>
                                                                        </t>
                                                                    </t>
                                                                </t>
                                                            </tr>
                                                        </t>
                                                    </t>
                                                </t>
                                            </t>
                                        </t>
                                        <t t-else="">
                                            <tr class="border-bottom border-gainsboro">
                                                <th colspan="6">
                                                    <span class="ms-2">
                                                        Depreciation
                                                    </span>
                                                </th>
                                            </tr>
                                        </t>
                                        <tr class="border-bottom border-gainsboro">
                                            <th colspan="6">
                                                <span class="fw-bolder ms-2">
                                                    Total
                                                    Expenses
                                                </span>
                                            </th>
                                            <t t-foreach="state.datas"
                                               t-as="total_expense"
                                               t-key="total_expense_index">
                                                <th class="text-end">
                                                    <span class="fw-bolder">
                                                        <t t-esc="total_expense.total_expense"/>
                                                    </span>
                                                </th>
                                            </t>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
