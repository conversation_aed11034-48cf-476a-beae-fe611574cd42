<?xml version="1.0" encoding="UTF-8"?>
<odoo>
<!--Account Day Book Report View Form-->
    <record id="account_day_book_report_view_form" model="ir.ui.view">
        <field name="name">account.day.book.report.view.form</field>
        <field name="model">account.day.book.report</field>
        <field name="arch" type="xml">
            <form string="Cash Book Report">
                <field name="company_id" invisible="1"/>
                <group col="4">
                    <field name="target_move" widget="radio"/>
                    <newline/>
                    <field name="date_from"/>
                    <field name="date_to"/>
                </group>
                <group>
                    <field name="account_ids" widget="many2many_tags"/>
                    <field name="journal_ids" widget="many2many_tags" options="{'no_create': True}"/>
                </group>
                <footer>
                    <button name="check_report" string="Print" type="object" default_focus="1" class="oe_highlight"/>
                    <button string="Cancel" class="btn btn-default" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
<!--Action Account Day Book Report-->
    <record id="action_account_day_book_view" model="ir.actions.act_window">
        <field name="name">Day Book Report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.day.book.report</field>
        <field name="view_id" ref="account_day_book_report_view_form"/>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
<!--Menu Day Book-->
    <menuitem id="account_day_book_menu" name="Day Book" action="action_account_day_book_view"
              parent="base_accounting_kit.account_reports_daily_reports"/>
</odoo>
