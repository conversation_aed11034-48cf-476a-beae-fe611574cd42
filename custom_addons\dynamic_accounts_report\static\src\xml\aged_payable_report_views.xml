<?xml version="1.0" encoding="UTF-8" ?>
<templates>
    <t t-name="age_p_template_new" owl="1">
        <!-- Section contains a structure for the Aged Payable report, including
        a filter view and a table view. It has div elements for the filter view
        and table view, with respective classes for styling.-->
        <div class="container">
            <div class="fin_report">
                <!--  Filter View  -->
                <div class="filter_view_gl pt-3 pb-5">
                    <div class="">
                        <h2 class="text-uppercase" style="padding:10px">
                            <t t-esc="props.action.name"/>
                        </h2>
                    </div>
                    <div style="margin-right: 10px; margin-left: 10px;margin-bottom: 15px;display: flex;">
                        <div class="sub_container_left" style="width:70%;">
                            <div class="report_print">
                                <!-- Print (PDF) Button -->
                                <button type="button"
                                        class="btn btn-primary btn-report-print"
                                        t-on-click="printPdf"
                                        style="margin-right: 8px;">
                                    Print (PDF)
                                </button>
                                <!-- Export (XLSX) Button -->
                                <button type="button"
                                        class="btn btn-primary btn-report-print"
                                        t-on-click="print_xlsx">
                                    Export (XLSX)
                                </button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <!-- Time Range -->
                            <div class="time_range" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <!-- Date Range Dropdown -->
                                    <span class="fa fa-calendar" title="Dates"
                                          role="img"
                                          aria-label="Dates"/>
                                    Date Range
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <!-- Date Range Options -->
                                    <div class="list-group">
                                        <!-- Today Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'today'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            Today
                                        </button>
                                        <!-- Last Month Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'last-month-end'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            End of Last Month
                                        </button>
                                        <!-- Last Quarter Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'last-quarter-end'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            End of Last Quarter
                                        </button>
                                        <!-- Last Year Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'last-year-end'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            End of Last year
                                        </button>
                                        <!-- Separator -->
                                        <div role="separator"
                                             class="dropdown-divider"/>
                                        <label class="" for="date_to">Date
                                            :
                                        </label>
                                        <div class="input-group date"
                                             data-target-input="nearest">
                                            <input type="date" id="end_date"
                                                   t-ref="date_to"
                                                   t-on-input="applyFilter"
                                                   style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                   name="end_date"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Options Dropdown -->
                            <div class="option" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <span class="fa fa-glass" title="Accounts"
                                          role="img"
                                          aria-label="Dates"/>
                                    Options
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <div class="list-group">
                                        <!-- Unfold All Button -->
                                        <button class="report-filter-button"
                                                type="button"
                                                t-ref="unfoldButton"
                                                t-on-click="unfoldAll">
                                            Unfold All
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br/>
            <div>
                <div class="table_style" style="height: 650px; overflow-y: scroll;">
                    <!-- Table View -->
                    <div class="table_view_gl" style="right:20px;width:100%;"
                         t-ref="table_view_gl">
                        <div>
                            <div class="table_main_view">
                                <table cellspacing="0" width="100%">
                                    <thead>
                                        <tr class="o_heading">
                                            <th colspan="6"/>
                                            <th>Invoice Date</th>
                                            <th>Amount Currency</th>
                                            <th>Currency</th>
                                            <th>Account</th>
                                            <th>Expected Date</th>
                                            <th>At Date</th>
                                            <th>1-30</th>
                                            <th>31-60</th>
                                            <th>61-90</th>
                                            <th>91-120</th>
                                            <th>Older</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody t-ref="tbody">
                                        <t t-if="state.move_line">
                                            <t t-set="i" t-value="0"/>
                                            <t t-foreach="state.move_line"
                                               t-as="move_line"
                                               t-key="move_line_index">
                                                <t t-set="i" t-value="i + 1"/>
                                                <tr class="border-bottom border-dark border-gainsboro">
                                                    <th>
                                                        <div data-bs-toggle="collapse"
                                                             t-attf-href="#move_line-{{i}}"
                                                             aria-expanded="false"
                                                             t-attf-aria-controls="move_line-{{i}}"
                                                             class="ms-3 collapsed">
                                                            <a class="btn header o_heading">
                                                                <span class="toggle-icon">
                                                                    <i class="fa fa-caret-down"/>
                                                                </span>
                                                                <t t-esc="move_line"/>
                                                            </a>
                                                        </div>
                                                    </th>
                                                    <th colspan="5">
                                                        <!-- Open Partner Button -->
                                                        <button t-att-data-id="state.total[move_line]['partner_id']"
                                                                class="btn bg-secondary"
                                                                style="margin-left: 3px"
                                                                t-on-click="openPartner">
                                                            <i class="fa fa-arrow-right"/>
                                                            Open
                                                        </button>
                                                        <!-- Journal Items Button -->
                                                        <button t-att-data-id="state.total[move_line]['partner_id']"
                                                                class="btn bg-secondary"
                                                                style="margin-left: 3px"
                                                                t-on-click="gotoJournalItem">
                                                            <i class="fa fa-arrow-right"/>
                                                            Journal Items
                                                        </button>
                                                    </th>
                                                    <th/>
                                                    <th/>
                                                    <th/>
                                                    <th/>
                                                    <th/>
                                                    <th>
                                                        <span>
                                                            <t t-if="state.total[move_line]['diff0_sum']"
                                                               t-esc="state.total[move_line]['currency_id']"/>
                                                            <t t-if="state.total[move_line]['diff0_sum']"
                                                               t-esc="state.total[move_line]['diff0_sum']"/>
                                                        </span>
                                                    </th>
                                                    <th>
                                                        <span>
                                                            <t t-if="state.total[move_line]['diff1_sum']"
                                                               t-esc="state.total[move_line]['currency_id']"/>
                                                            <t t-if="state.total[move_line]['diff1_sum']"
                                                               t-esc="state.total[move_line]['diff1_sum']"/>
                                                        </span>
                                                    </th>
                                                    <th>
                                                        <span>
                                                            <t t-if="state.total[move_line]['diff2_sum']"
                                                               t-esc="state.total[move_line]['currency_id']"/>
                                                            <t t-if="state.total[move_line]['diff2_sum']"
                                                               t-esc="state.total[move_line]['diff2_sum']"/>
                                                        </span>
                                                    </th>
                                                    <th>
                                                        <span>
                                                            <t t-if="state.total[move_line]['diff3_sum']"
                                                               t-esc="state.total[move_line]['currency_id']"/>
                                                            <t t-if="state.total[move_line]['diff3_sum']"
                                                               t-esc="state.total[move_line]['diff3_sum']"/>
                                                        </span>
                                                    </th>
                                                    <th>
                                                        <span>
                                                            <t t-if="state.total[move_line]['diff4_sum']"
                                                               t-esc="state.total[move_line]['currency_id']"/>
                                                            <t t-if="state.total[move_line]['diff4_sum']"
                                                               t-esc="state.total[move_line]['diff4_sum']"/>
                                                        </span>
                                                    </th>
                                                    <th>
                                                        <span>
                                                            <t t-if="state.total[move_line]['diff5_sum']"
                                                               t-esc="state.total[move_line]['currency_id']"/>
                                                            <t t-if="state.total[move_line]['diff5_sum']"
                                                               t-esc="state.total[move_line]['diff5_sum']"/>
                                                        </span>
                                                    </th>
                                                    <th>
                                                        <span>
                                                            <t t-if="state.total[move_line]['credit_sum']"
                                                               t-esc="state.total[move_line]['currency_id']"/>
                                                            <t t-if="state.total[move_line]['credit_sum']"
                                                               t-esc="state.total[move_line]['credit_sum']"/>
                                                        </span>
                                                    </th>
                                                </tr>
                                                <t t-foreach="state.data[move_line]"
                                                   t-as="valuelist"
                                                   t-key="valuelist_index">
                                                    <tr class="border-bottom border-gainsboro collapse"
                                                        t-attf-id="move_line-{{i}}">
                                                        <th colspan="6">
                                                            <span style="gap: 12px;display: flex;">
                                                                <t t-esc="valuelist['move_name']"/>
                                                                <t t-esc="valuelist['name']"/>
                                                                <a type="button"
                                                                   class="dropdown-toggle"
                                                                   data-bs-toggle="dropdown">
                                                                </a>
                                                                <div class="dropdown-menu  journals">
                                                                    <button t-att-data-id="valuelist['move_id'][0]"
                                                                            type="button"
                                                                            t-on-click="gotoJournalEntry"
                                                                            style="border: none;
                                                                            background-color: inherit;
                                                                            padding: 4px 8px;
                                                                            font-size: 16px;
                                                                            cursor: pointer;
                                                                            display: inline-block;">
                                                                        View
                                                                        Journal
                                                                        Entry
                                                                    </button>
                                                                </div>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-esc="valuelist['date']"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-esc="valuelist['amount_currency']"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-esc="valuelist['currency_id'][1]"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-esc="valuelist['account_id'][1]"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-if="valuelist['date_maturity']"
                                                                   t-esc="valuelist['date_maturity']"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-if="valuelist['diff0']"
                                                                   t-esc="state.total[move_line]['currency_id']"/>
                                                                <t t-if="valuelist['diff0']"
                                                                   t-esc="valuelist['diff0']"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-if="valuelist['diff1']"
                                                                   t-esc="state.total[move_line]['currency_id']"/>
                                                                <t t-if="valuelist['diff1']"
                                                                   t-esc="valuelist['diff1']"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-if="valuelist['diff2']"
                                                                   t-esc="state.total[move_line]['currency_id']"/>
                                                                <t t-if="valuelist['diff2']"
                                                                   t-esc="valuelist['diff2']"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-if="valuelist['diff3']"
                                                                   t-esc="state.total[move_line]['currency_id']"/>
                                                                <t t-if="valuelist['diff3']"
                                                                   t-esc="valuelist['diff3']"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-if="valuelist['diff4']"
                                                                   t-esc="state.total[move_line]['currency_id']"/>
                                                                <t t-if="valuelist['diff4']"
                                                                   t-esc="valuelist['diff4']"/>
                                                            </span>
                                                        </th>
                                                        <th>
                                                            <span>
                                                                <t t-if="valuelist['diff5']"
                                                                   t-esc="state.total[move_line]['currency_id']"/>
                                                                <t t-if="valuelist['diff5']"
                                                                   t-esc="valuelist['diff5']"/>
                                                            </span>
                                                        </th>
                                                        <th/>
                                                    </tr>
                                                </t>
                                            </t>
                                        </t>
                                        <tr>
                                            <th/>
                                            <th colspan="10" class="o_heading">
                                                Total
                                            </th>
                                            <th class="o_heading">
                                                <t t-esc="state.currency"/>
                                                <t t-out="state.diff0_sum"/>
                                            </th>
                                            <th class="o_heading">
                                                <t t-esc="state.currency"/>
                                                <t t-out="state.diff1_sum"/>
                                            </th>
                                            <th class="o_heading">
                                                <t t-esc="state.currency"/>
                                                <t t-out="state.diff2_sum"/>
                                            </th>
                                            <th class="o_heading">
                                                <t t-esc="state.currency"/>
                                                <t t-out="state.diff3_sum"/>
                                            </th>
                                            <th class="o_heading">
                                                <t t-esc="state.currency"/>
                                                <t t-out="state.diff4_sum"/>
                                            </th>
                                            <th class="o_heading">
                                                <t t-esc="state.currency"/>
                                                <t t-out="state.diff5_sum"/>
                                            </th>
                                            <th class="o_heading">
                                                <t t-esc="state.currency"/>
                                                <t t-out="state.total_credit"/>
                                            </th>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
