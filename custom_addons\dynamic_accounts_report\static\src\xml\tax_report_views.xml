<?xml version="1.0" encoding="UTF-8" ?>
<templates>
    <t t-name="tax_r_template_new" owl="1">
        <!-- Section contains a structure for the Trial Balance report,
        including a filter view and a table view. It has div elements for the
        filter view and table view,with respective classes for styling.-->
        <div class="container">
            <div class="fin_report">
                <!--  Filter View  -->
                <div class="filter_view pt-3 pb-5">
                    <div class="">
                        <h2 class="text-uppercase" style="padding:10px">
                            <t t-esc="props.action.name"/>
                        </h2>
                    </div>
                    <div style="margin-right: 10px; margin-left: 10px;margin-bottom: 15px;display: flex;">
                        <div class="sub_container_left" style="width:70%;">
                            <div class="report_print">
                                <!-- Print (PDF) Button -->
                                <button type="button"
                                        class="btn btn-primary btn-report-print"
                                        t-on-click="printPdf"
                                        style="margin-right: 8px;">
                                    Print (PDF)
                                </button>
                                <!-- Export (XLSX) Button -->
                                <button type="button"
                                        class="btn btn-primary btn-report-print"
                                        t-on-click="print_xlsx">
                                    Export (XLSX)
                                </button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <!-- Time Range -->
                            <div class="time_range" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <!-- Date Range Dropdown -->
                                    <span class="fa fa-calendar" title="Dates"
                                          role="img"
                                          aria-label="Dates"/>
                                    Date Range
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <!-- Date Range Options -->
                                    <div class="list-group">
                                        <!-- This Month Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'month'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            This Month
                                        </button>
                                        <!-- This Quarter Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'quarter'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            This Quarter
                                        </button>
                                        <!-- This Year Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'year'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            This Year
                                        </button>
                                        <!-- Separator -->
                                        <div role="separator"
                                             class="dropdown-divider"/>
                                        <!-- Last Month Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'last-month'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            Last month
                                        </button>
                                        <!-- Last Quarter Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'last-quarter'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            Last quarter
                                        </button>
                                        <!-- Last Year Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'last-year'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            Last year
                                        </button>
                                        <!-- Separator -->
                                        <div role="separator"
                                             class="dropdown-divider"/>
                                        <!-- Start Date -->
                                        <label class="" for="date_from">Start
                                            Date :
                                        </label>
                                        <div class="input-group date"
                                             data-target-input="nearest">

                                            <input type="date" id="start_date"
                                                   t-on-change="applyFilter"
                                                   t-ref="date_from"
                                                   style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                   name="start_date"/>
                                        </div>
                                        <!-- End Date -->
                                        <label class="" for="date_to">End Date
                                            :
                                        </label>
                                        <div class="input-group date"
                                             data-target-input="nearest">
                                            <input type="date" id="end_date"
                                                   t-on-change="applyFilter"
                                                   t-ref="date_to"
                                                   style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                   name="end_date"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="comparison_range" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <span class="fa fa-signal"
                                          title="Comparison"
                                          role="img"
                                          aria-label="Comparison"/>
                                    Comparison
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <div class="list-group">
                                        <button class="report-filter-button"
                                                t-att-data-value="'no comparison'"
                                                type="button"
                                                t-on-click="applyComparison">
                                            No Comparison
                                        </button>
                                        <div role="separator"
                                             class="dropdown-divider"/>
                                        <label class="" for="periods"
                                               style="display: flex;padding: 4px 20px;color: #000;">
                                            Previous Period
                                            <br/>
                                            Number of Periods:
                                        </label>
                                        <div class="input-group comparison"
                                             data-target-input="nearest">

                                            <input type="number"
                                                   t-ref="periods"
                                                   min="1"
                                                   t-on-input="onPeriodChange"
                                                   style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                   name="previous_period"/>
                                        </div>
                                        <button class="btn btn-primary"
                                                type="button"
                                                style="margin-left: 19px;margin-top: 4px;"
                                                t-on-click="applyComparisonPeriod">
                                            Apply
                                        </button>
                                        <div role="separator"
                                             class="dropdown-divider"/>
                                        <label class="" for="period_year"
                                               style="display: flex;padding: 4px 20px;color: #000;">
                                            Same Period Last Year
                                            <br/>
                                            Number of Periods:
                                        </label>
                                        <div class="input-group comparison"
                                             data-target-input="nearest">
                                            <input type="number"
                                                   t-ref="period_year"
                                                   t-on-input="onPeriodYearChange"
                                                   min="1"
                                                   style="border:none;border-bottom: 1px solid #000;padding: 5px;outline: none;"
                                                   name="period_year"/>
                                        </div>
                                        <button class="btn btn-primary"
                                                type="button"
                                                style="margin-left: 19px;margin-top: 4px;"
                                                t-on-click="applyComparisonYear">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- Options Dropdown -->
                            <div class="option" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <span class="fa fa-glass" title="Accounts"
                                          role="img"
                                          aria-label="Dates"/>
                                    Options : Posted Entries
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <div class="list-group">
                                        <!-- Include Draft Entries Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'draft'"
                                                type="button"
                                                t-on-click="applyFilter">
                                            Include Draft Entries
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- Options Dropdown -->
                            <div class="option" style="">
                                <a type="button" class="dropdown-toggle"
                                   data-bs-toggle="dropdown">
                                    <span class="fa fa-glass" title="Accounts"
                                          role="img"
                                          aria-label="Dates"/>
                                    Reports :
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <div class="list-group">
                                        <!-- Include Draft Entries Button -->
                                        <button class="report-filter-button"
                                                t-att-data-value="'global'"
                                                type="button" t-ref="global"
                                                t-on-click="applyFilter">
                                            Generic Tax Report
                                        </button>
                                        <button class="report-filter-button"
                                                t-att-data-value="'account tax'"
                                                type="button" t-ref="account"
                                                t-on-click="applyFilter">
                                            Group by: Account > Tax
                                        </button>
                                        <button class="report-filter-button"
                                                t-att-data-value="'tax account'"
                                                type="button" t-ref="tax"
                                                t-on-click="applyFilter">
                                            Group by: Tax > Account
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <!-- Table View -->
                <div class="table_view_gl" style="right:20px;height: 650px; overflow-y: scroll;"
                     t-ref="table_view_gl">
                    <div>
                        <div class="table_main_view">
                            <table cellspacing="0" width="100%">
                                <thead>
                                    <tr class="o_heading"
                                        style="text-align:center;">
                                        <th colspan="7"/>
                                        <t t-if="state.date_viewed.length != 0">
                                            <t t-foreach="state.date_viewed"
                                               t-as="date_view"
                                               t-key="date_view_index">
                                                <th colspan="2">
                                                    <t t-esc="date_view"/>
                                                </th>
                                            </t>
                                        </t>
                                    </tr>
                                    <tr class="o_heading"
                                        style="text-align:center;">
                                        <th colspan="6"/>
                                        <th/>
                                        <t t-if="state.apply_comparison == true">
                                            <t t-set="number_of_periods"
                                               t-value="comparison_number_range"/>
                                            <t t-foreach="number_of_periods"
                                               t-as="number" t-key="number">
                                                <th>NET</th>
                                                <th>TAX</th>
                                            </t>
                                        </t>
                                        <th>NET</th>
                                        <th>TAX</th>
                                    </tr>
                                </thead>
                                <tbody t-ref="tbody">
                                    <t t-if="state.data">
                                        <t t-set="prev_account" t-value="None"/>
                                        <t t-set="prev_tax" t-value="None"/>
                                        <tr class="border-bottom"
                                            style="border-spacing: 0 10px;background:#dfdfdf;">
                                            <th colspan="6">
                                                <span style="font-weight: 700;">
                                                    Sales
                                                </span>
                                            </th>
                                            <th/>
                                            <th/>
                                            <t t-if="state.apply_comparison == true">
                                                <t t-set="number_of_periods"
                                                   t-value="comparison_number_range"/>
                                                <t t-foreach="number_of_periods"
                                                   t-as="no" t-key="no">
                                                    <th/>
                                                    <th/>
                                                </t>
                                            </t>
                                            <th style="text-align:center;font-weight: 700;">
                                                <t t-esc="state.sale_total.toFixed(2)"/>
                                            </th>
                                        </tr>
                                        <t t-set="i" t-value="0"/>
                                        <t t-foreach="state.data.sale"
                                           t-as="sale_tax_line"
                                           t-key="sale_tax_line_index">
                                            <t t-set="i" t-value="i + 1"/>
                                            <t t-if="state.report_type">
                                                <t t-if="Object.keys(state.report_type) == 'account'">
                                                    <t t-if="prev_account !== sale_tax_line['account']">
                                                        <t t-set="prev_account"
                                                           t-value="sale_tax_line['account']"/>
                                                        <tr class="border-bottom"
                                                            style="border-spacing: 0 10px;">
                                                            <th colspan="6">
                                                                <span>
                                                                    <t t-esc="sale_tax_line['account']"/>
                                                                </span>
                                                            </th>
                                                        </tr>
                                                    </t>
                                                </t>
                                                <t t-else="Object.keys(state.report_type) == 'tax'">
                                                    <t t-if="prev_tax !== sale_tax_line.name">
                                                        <t t-set="prev_tax"
                                                           t-value="sale_tax_line.name"/>
                                                        <tr class="border-bottom"
                                                            style="border-spacing: 0 10px;">
                                                            <th colspan="6">
                                                                <span>
                                                                    <t t-esc="sale_tax_line.name"/>
                                                                    (
                                                                    <t t-esc="sale_tax_line.amount"/>
                                                                    %)
                                                                </span>
                                                            </th>
                                                        </tr>
                                                    </t>
                                                </t>
                                            </t>
                                            <tr class="border-bottom"
                                                style="border-spacing: 0 10px;font-weight: 400;">
                                                <th colspan="6">
                                                    <span style="font-weight: 400;">
                                                        <t t-if="state.report_type and Object.keys(state.report_type) == 'tax'">
                                                            <t t-esc="sale_tax_line['account']"/>
                                                        </t>
                                                        <t t-else="">
                                                            <t t-esc="sale_tax_line.name"/>
                                                            (
                                                            <t t-esc="sale_tax_line.amount"/>
                                                            %)
                                                        </t>
                                                    </span>
                                                </th>
                                                <th/>
                                                <t t-if="state.apply_comparison == true">
                                                    <t t-if="sale_tax_line['dynamic net']">
                                                        <t t-set="number_of_periods"
                                                           t-value="comparison_number_range"/>
                                                        <t t-foreach="number_of_periods"
                                                           t-as="num"
                                                           t-key="num">
                                                            <th style="text-align:center;font-weight: 400;">
                                                                <t t-if="sale_tax_line['dynamic net']['dynamic_total_net_sum' + num]"
                                                                   t-esc="sale_tax_line['dynamic net']['dynamic_total_net_sum' + num]"/>
                                                            </th>
                                                            <th style="text-align:center;font-weight: 400;">
                                                                <t t-if="sale_tax_line['dynamic tax']['dynamic_total_tax_sum' + num]"
                                                                   t-esc="sale_tax_line['dynamic tax']['dynamic_total_tax_sum' + num]"/>
                                                            </th>
                                                        </t>
                                                    </t>
                                                </t>
                                                <th style="text-align:center;">
                                                    <span style="font-weight: 400;">
                                                        <t t-esc="sale_tax_line.net"/>
                                                    </span>
                                                </th>
                                                <th style="text-align:center;">
                                                    <span style="font-weight: 400;">
                                                        <t t-esc="sale_tax_line.tax"/>
                                                    </span>
                                                </th>
                                            </tr>
                                        </t>
                                        <tr style="height: 2rem;"/>
                                        <tr class="border-bottom"
                                            style="border-spacing: 0 10px;background:#dfdfdf;">
                                            <th colspan="6">
                                                <span style="font-weight: 700;">
                                                    Purchase
                                                </span>
                                            </th>
                                            <th/>
                                            <th/>
                                            <t t-if="state.apply_comparison == true">
                                                <t t-set="number_of_periods"
                                                   t-value="comparison_number_range"/>
                                                <t t-foreach="number_of_periods"
                                                   t-as="numb" t-key="numb">
                                                    <th/>
                                                    <th/>
                                                </t>
                                            </t>
                                            <th style="text-align:center;font-weight: 700;">
                                                <t t-esc="state.purchase_total.toFixed(2)"/>
                                            </th>
                                        </tr>
                                        <t t-set="i" t-value="0"/>
                                        <t t-foreach="state.data.purchase"
                                           t-as="purchase_tax_line"
                                           t-key="purchase_tax_line_index">
                                            <t t-set="i" t-value="i + 1"/>
                                            <t t-if="state.report_type">
                                                <t t-if="Object.keys(state.report_type) == 'account'">
                                                    <tr class="border-bottom"
                                                        style="border-spacing: 0 10px;">
                                                        <th colspan="6">
                                                            <span>
                                                                <t t-esc="purchase_tax_line['account']"/>
                                                            </span>
                                                        </th>
                                                    </tr>
                                                </t>
                                                <t t-else="Object.keys(state.report_type) == 'tax'">
                                                    <t t-set="prev_account"
                                                       t-value="purchase_tax_line.name"/>
                                                    <tr class="border-bottom"
                                                        style="border-spacing: 0 10px;">
                                                        <th colspan="6">
                                                            <span>
                                                                <t t-esc="purchase_tax_line.name"/>
                                                                (
                                                                <t t-esc="purchase_tax_line.amount"/>
                                                                %)
                                                            </span>
                                                        </th>
                                                    </tr>
                                                </t>
                                            </t>
                                            <tr class="border-bottom"
                                                style="border-spacing: 0 10px;">
                                                <th colspan="6">
                                                    <span style="font-weight: 400;">
                                                        <t t-if="state.report_type and Object.keys(state.report_type) == 'tax'">
                                                            <t t-esc="purchase_tax_line['account']"/>
                                                        </t>
                                                        <t t-else="">
                                                            <t t-esc="purchase_tax_line.name"/>
                                                            (
                                                            <t t-esc="purchase_tax_line.amount"/>
                                                            %)
                                                        </t>
                                                    </span>
                                                </th>
                                                <th/>
                                                <t t-if="state.apply_comparison == true">
                                                    <t t-if="purchase_tax_line['dynamic net']">
                                                        <t t-set="number_of_periods"
                                                           t-value="comparison_number_range"/>
                                                        <t t-foreach="number_of_periods"
                                                           t-as="period"
                                                           t-key="period">
                                                            <th style="text-align:center;font-weight: 400;">
                                                                <t t-if="purchase_tax_line['dynamic net']['dynamic_total_net_sum' + period]"
                                                                   t-esc="purchase_tax_line['dynamic net']['dynamic_total_net_sum' + period]"/>
                                                            </th>
                                                            <th style="text-align:center;font-weight: 400;">
                                                                <t t-if="purchase_tax_line['dynamic tax']['dynamic_total_tax_sum' + period]"
                                                                   t-esc="purchase_tax_line['dynamic tax']['dynamic_total_tax_sum' + period]"/>
                                                            </th>
                                                        </t>
                                                    </t>
                                                </t>
                                                <th style="text-align:center;">
                                                    <span style="font-weight: 400;">
                                                        <t t-esc="purchase_tax_line.net"/>
                                                    </span>
                                                </th>
                                                <th style="text-align:center;">
                                                    <span style="font-weight: 400;">
                                                        <t t-esc="purchase_tax_line.tax"/>
                                                    </span>
                                                </th>
                                            </tr>
                                        </t>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
