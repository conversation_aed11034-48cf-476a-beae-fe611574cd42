# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* dynamic_accounts_report
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-28 11:11+0000\n"
"PO-Revision-Date: 2023-11-28 11:11+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "1-30"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "31-60"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "61-90"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "91-120"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-1\">\n"
"                                    Expenses\n"
"                                </span>"
msgstr "Pengeluaran"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">\n"
"                                    Income\n"
"                                </span>"
msgstr "Penghasilan"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">\n"
"                                    Total Income\n"
"                                </span>"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">Total Expenses\n"
"                                    </span>"
msgstr "Jumlah Biaya"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-3\">\n"
"                                    Gross Profit\n"
"                                </span>"
msgstr "Laba kotor"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder\">\n"
"                                    Net Profit\n"
"                                </span>"
msgstr "Laba bersih"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Cost of\n"
"                                    Revenue\n"
"                                </span>"
msgstr "Pendapatan"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Depreciation\n"
"                                    </span>"
msgstr "Depresiasi"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "<span class=\"ms-3\">Expenses</span>"
msgstr "Pengeluaran"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "<span class=\"ms-3\">Operating Income</span>"
msgstr "Pendapatan operasional"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Other Income\n"
"                                </span>"
msgstr "Penghasilan lain"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "<span style=\"font-weight: 700;\">Purchase</span>"
msgstr "Pembelian"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "<span style=\"font-weight: 700;\">Sales</span>"
msgstr "Penjualan"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        ASSETS\n"
"                                    </span>"
msgstr "AKTIVA"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        EQUITY\n"
"                                    </span>"
msgstr "EKUITAS"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        LIABILITIES\n"
"                                    </span>"
msgstr "KEWAJIBAN"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Current Assets\n"
"                                    </span>"
msgstr "Aset lancar"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Current Liabilities\n"
"                                    </span>"
msgstr "Kewajiban Lancar"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        LIABILITIES + EQUITY\n"
"                                    </span>"
msgstr "KEWAJIBAN + EKUITAS"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Assets\n"
"                                    </span>"
msgstr "Total aset"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Current Assets\n"
"                                    </span>"
msgstr "Total aset saat ini"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Current Liabilities\n"
"                                    </span>"
msgstr "Jumlah Kewajiban Lancar"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total EQUITY\n"
"                                    </span>"
msgstr "Total EKUITAS"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total LIABILITIES\n"
"                                    </span>"
msgstr "Jumlah KEWAJIBAN"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Unallocated Earnings\n"
"                                    </span>"
msgstr "Total Pendapatan yang Belum Dialokasikan"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Unallocated Earnings\n"
"                                    </span>"
msgstr "Penghasilan yang Tidak Dialokasikan"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Bank and Cash Accounts\n"
"                                    </span>"
msgstr "Rekening Bank dan Kas"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Allocated Earnings\n"
"                                    </span>"
msgstr "Pendapatan yang Dialokasikan Saat Ini"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Assets\n"
"                                    </span>"
msgstr "Aset lancar"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Earnings\n"
"                                    </span>"
msgstr "Penghasilan Saat Ini"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Liabilities\n"
"                                    </span>"
msgstr "Kewajiban Lancar"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Payables\n"
"                                    </span>"
msgstr "Hutang"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Fixed Assets\n"
"                                    </span>"
msgstr "Ditambah Aset Tetap"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Non-current Assets\n"
"                                    </span>"
msgstr "Ditambah Aset Tidak Lancar"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Non-current Liabilities\n"
"                                    </span>"
msgstr "Ditambah Kewajiban Tidak Lancar"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Prepayments\n"
"                                    </span>"
msgstr "Pembayaran di muka"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Receivables\n"
"                                    </span>"
msgstr "Piutang"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Retained Earnings\n"
"                                    </span>"
msgstr "Pendapatan yang disimpan"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
msgid ""
"<span>Unknown\n"
"                                                            Account\n"
"                                                        </span>"
msgstr "Akun Tidak Dikenal"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
msgid ""
"<span>Unknown\n"
"                                                            Partner\n"
"                                                        </span>"
msgstr "Mitra Tidak Dikenal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "ASSETS"
msgstr "AKTIVA"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Account"
msgstr "Akun"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_bank_book_report
msgid "Account Bank Book Report"
msgstr "Laporan Buku Bank Rekening"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_cash_book_report
msgid "Account Cash Book Report"
msgstr "Laporan Buku Kas Rekening"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__account_ids
#, python-format
msgid "Accounts"
msgstr "Akun"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Accounts:"
msgstr "Akun:"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_aged_payable
msgid "Age Payable"
msgstr "Hutang Usia"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_aged_receivable
msgid "Age Receivable"
msgstr "Usia Piutang"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_aged_payable
#: model:ir.ui.menu,name:dynamic_accounts_report.aged_payable_menu
msgid "Aged Payable"
msgstr "Hutang Berumur"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_age_payable_report
msgid "Aged Payable Report"
msgstr "Laporan Hutang Berumur"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_aged_receivable
#: model:ir.ui.menu,name:dynamic_accounts_report.aged_receivable_menu
msgid "Aged Receivable"
msgstr "Piutang Berumur"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_age_receivable_report
msgid "Aged Receivable Report"
msgstr "Laporan Umur Piutang"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Amount Currency"
msgstr "Jumlah Mata Uang"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "Analytic Account"
msgstr "Akun Analitik"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__analytic_ids
msgid "Analytic Accounts"
msgstr "Akun Analitik"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Analytic Accounts:"
msgstr "Akun Analitik:"

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__analytic_ids
msgid "Analytic accounts associated with the current record."
msgstr "Akun analitik yang terkait dengan catatan saat ini."

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Apply"
msgstr "Menerapkan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "At Date"
msgstr "Pada Tanggal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#, python-format
msgid "Balance"
msgstr "Keseimbangan"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_dynamic_balance_sheet
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_balance_sheet
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_balance_sheet_report
msgid "Balance Sheet"
msgstr "Neraca keuangan"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_bank_book
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_bank_book
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_bank_book
msgid "Bank Book"
msgstr "Buku bank"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Bank and\n"
"                                                    Cash Accounts"
msgstr "Rekening Bank dan Kas"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Bank and Cash Accounts"
msgstr "Rekening Bank dan Kas"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_cash_book
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_cash_book
msgid "Cash Book"
msgstr "Buku Kas"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Cash basis method"
msgstr "Metode berbasis tunai"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#, python-format
msgid "Communication"
msgstr "Komunikasi"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Comparison"
msgstr "Perbandingan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Cost\n"
"                                                                of\n"
"                                                                Revenue"
msgstr "Biaya Pendapatan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Cost of\n"
"                                                        Revenue"
msgstr "Biaya Pendapatan"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Credit"
msgstr "Kredit"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Currency"
msgstr "Mata uang"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Allocated Earnings"
msgstr "Pendapatan yang Dialokasikan Saat Ini"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Assets"
msgstr "Aset lancar"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Earnings"
msgstr "Penghasilan Saat Ini"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current Allocated\n"
"                                                            Earnings"
msgstr "Pendapatan yang Dialokasikan Saat Ini"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Current Assets"
msgstr "Aset lancar"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Current Liabilities"
msgstr "Kewajiban Lancar"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#, python-format
msgid "Date"
msgstr "Tanggal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid ""
"Date\n"
"                                            :"
msgstr "Tanggal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Date Range"
msgstr "Rentang Tanggal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Dates"
msgstr "tanggal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Debit"
msgstr "Debet"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Depreciation"
msgstr "Depresiasi"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__display_name
msgid "Display Name"
msgstr "Nama tampilan"

#. module: dynamic_accounts_report
#: model:ir.model.fields.selection,name:dynamic_accounts_report.selection__dynamic_balance_sheet_report__target_move__draft
msgid "Draft"
msgstr "Draf"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Due Date"
msgstr "Tenggat waktu"

#. module: dynamic_accounts_report
#: model:ir.ui.menu,name:dynamic_accounts_report.dynamic_report_accounting
msgid "Dynamic Financial Reports"
msgstr "Laporan Keuangan Dinamis"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "EQUITY"
msgstr "EKUITAS"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"End\n"
"                                                Date\n"
"                                                :"
msgstr "Tanggal Berakhir:"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "End Balance"
msgstr "Saldo Akhir"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid ""
"End Date\n"
"                                            :"
msgstr "Tanggal Berakhir"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_to
msgid "End date"
msgstr "Tanggal akhir"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last Month"
msgstr "Akhir Bulan Lalu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last Quarter"
msgstr "Akhir Kuartal Terakhir"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last year"
msgstr "Akhir tahun lalu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Entry label"
msgstr "Label entri"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Expected Date"
msgstr "Tanggal yang Diharapkan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Expenses"
msgstr "Pengeluaran"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Export (XLSX)"
msgstr "Ekspor (XLSX)"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"General\n"
"                                                                                Ledger"
msgstr "Jurnal umum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"General\n"
"                                                                    Ledger"
msgstr "Jurnal umum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.actions.client,name:dynamic_accounts_report.action_general_ledger
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_general_ledger
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_general_ledger
#, python-format
msgid "General Ledger"
msgstr "Jurnal umum"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Laporan Buku Besar Umum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Generic Tax Report"
msgstr "Laporan Pajak Umum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Gross Profit"
msgstr "Laba kotor"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Group by: Account > Tax"
msgstr "Kelompokkan berdasarkan: Akun > Pajak"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Group by: Tax > Account"
msgstr "Kelompokkan berdasarkan: Pajak > Rekening"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__id
msgid "ID"
msgstr "PENGENAL"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Include Draft Entries"
msgstr "Sertakan Entri Draf"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Income"
msgstr "Penghasilan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Initial Balance"
msgstr "Saldo Awal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Invoice Date"
msgstr "Tanggal faktur"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "JRNL"
msgstr "JRNL"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Journal"
msgstr "Jurnal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Journal Items"
msgstr "Item Jurnal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__journal_ids
#, python-format
msgid "Journals"
msgstr "Jurnal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Journals:"
msgstr "Jurnal:"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "LIABILITIES"
msgstr "LIABILITIES"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "LIABILITIES + EQUITY"
msgstr "KEWAJIBAN + EKUITAS"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui saat"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last month"
msgstr "Bulan lalu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last quarter"
msgstr "Kuartal terakhir"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last year"
msgstr "Tahun lalu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Matching Number"
msgstr "Nomor yang Cocok"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Move"
msgstr "Bergerak"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#, python-format
msgid "NET"
msgstr "BERSIH"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Net Profit"
msgstr "Laba bersih"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "No Comparison"
msgstr "Tidak ada perbandingan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Number of Periods:"
msgstr "Jumlah Periode:"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Older"
msgstr "Lebih tua"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Open"
msgstr "Membuka"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Operating\n"
"                                                                Income"
msgstr "Pendapatan operasional"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Operating\n"
"                                                        Income"
msgstr "Pendapatan operasional"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Options"
msgstr "Pilihan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Options : Posted Entries"
msgstr "Opsi : Entri yang Diposting"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Options : Posted Entries ,"
msgstr "Opsi : Entri yang Diposting ,"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#, python-format
msgid "Options :Posted Entries"
msgstr "Opsi : Entri yang Diposting"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#, python-format
msgid "Options :Posted Entries ,"
msgstr "Opsi: Entri yang Diposting,"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Other\n"
"                                                        Income"
msgstr "Penghasilan lain"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Other Income"
msgstr "Penghasilan lain"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Partner"
msgstr "Mitra"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_partner_ledger
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_partner_ledger
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_partner_ledger
msgid "Partner Ledger"
msgstr "Buku Besar Mitra"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_partner_ledger
msgid "Partner Ledger Report"
msgstr "Laporan Buku Besar Mitra"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Payable"
msgstr "Hutang"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Payables"
msgstr "Hutang"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus\n"
"                                                    Non-current Assets"
msgstr "Ditambah Aset Tidak Lancar"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus\n"
"                                                    Non-current Liabilities"
msgstr "Ditambah Kewajiban Tidak Lancar"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus Fixed\n"
"                                                    Assets"
msgstr "Ditambah Aset Tetap"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Fixed Assets"
msgstr "Ditambah Aset Tetap"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Non-current Assets"
msgstr "Ditambah Aset Tidak Lancar"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Non-current Liabilities"
msgstr "Ditambah Kewajiban Tidak Lancar"

#. module: dynamic_accounts_report
#: model:ir.model.fields.selection,name:dynamic_accounts_report.selection__dynamic_balance_sheet_report__target_move__posted
msgid "Posted"
msgstr "Diposting"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
msgid "Posted ,"
msgstr "Diposting ,"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Posted Entries"
msgstr "Entri yang Diposting"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Prepayments"
msgstr "Pembayaran di muka"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Previous Period"
msgstr "Periode sebelumnya"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Print (PDF)"
msgstr "Cetak (PDF)"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_profit_loss
msgid "Profit And Loss"
msgstr "Laba rugi"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_dynamic_balance_sheet_report
msgid "Profit Loss Report"
msgstr "Laporan Laba Rugi"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_dynamic_profit_and_loss
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_profit_and_loss_report
msgid "Profit and Loss"
msgstr "Laba rugi"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Purchase"
msgstr "Pembelian"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Receivable"
msgstr "Piutang"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Receivables"
msgstr "Piutang"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Reference"
msgstr "Referensi"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "Report"
msgstr "Laporan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Reports :"
msgstr "Laporan :"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Retained\n"
"                                                    Earnings"
msgstr "Pendapatan yang disimpan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Retained Earnings"
msgstr "Pendapatan yang disimpan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Sales"
msgstr "Penjualan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Same Period Last Year"
msgstr "Periode yang Sama Tahun Lalu"

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__account_ids
msgid "Select one or more accounts."
msgstr "Pilih satu atau lebih akun."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__journal_ids
msgid "Select one or more journals."
msgstr "Pilih satu atau lebih jurnal."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__company_id
msgid "Select the company to which this record belongs."
msgstr "Pilih perusahaan yang memiliki catatan ini."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__target_move
msgid "Select the target move status."
msgstr "Pilih status pemindahan target."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_to
msgid "Specify the end date."
msgstr "Tentukan tanggal akhir."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_from
msgid "Specify the start date."
msgstr "Tentukan tanggal mulai."

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Start\n"
"                                                Date :"
msgstr "Mulai tanggal :"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid ""
"Start\n"
"                                            Date :"
msgstr "Mulai tanggal :"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_from
msgid "Start date"
msgstr ""

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#, python-format
msgid "TAX"
msgstr "PAJAK"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__target_move
msgid "Target Move"
msgstr "Gerakan Sasaran"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Target Move:"
msgstr "Gerakan Sasaran:"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "Target move"
msgstr "Gerakan sasaran"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_tax_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_tax_report
#: model:ir.model,name:dynamic_accounts_report.model_tax_report
#: model:ir.ui.menu,name:dynamic_accounts_report.tax_report_menu
msgid "Tax Report"
msgstr "Laporan Pajak"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Month"
msgstr "Bulan ini"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Quarter"
msgstr "Kuartal ini"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Year"
msgstr "Tahun ini"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "Today"
msgstr "Hari ini"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Total"
msgstr "Total"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Total\n"
"                                                    Expenses"
msgstr "Jumlah Biaya"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total ASSETS"
msgstr "Total aset"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Current Assets"
msgstr "Total aset saat ini"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Current Liabilities"
msgstr "Jumlah Kewajiban Lancar"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total EQUITY"
msgstr "Total EKUITAS"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Total Income"
msgstr "Jumlah pemasukan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total LIABILITIES"
msgstr "Total LIABILITIES"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Unallocated Earnings"
msgstr "Total Pendapatan yang Belum Dialokasikan"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_trial_balance
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_trial_balance
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_trial_balance
msgid "Trial Balance"
msgstr ""

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_trial_balance
msgid "Trial Balance Report"
msgstr "Laporan Neraca Percobaan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Unallocated Earnings"
msgstr "Penghasilan yang Tidak Dialokasikan"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Unfold All"
msgstr "Buka Semua"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#, python-format
msgid ""
"Unknown\n"
"                                                                    Accounts"
msgstr "Akun Tidak Dikenal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid ""
"Unknown\n"
"                                                                    Partner"
msgstr "Mitra Tidak Dikenal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#, python-format
msgid ""
"View\n"
"                                                                        Journal\n"
"                                                                        Entry"
msgstr "Lihat Entri Jurnal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid ""
"View\n"
"                                                                    Journal\n"
"                                                                    Entry"
msgstr "Lihat Entri Jurnal"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
msgid "posted,"
msgstr "diposting,"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
msgid "to"
msgstr "ke"
