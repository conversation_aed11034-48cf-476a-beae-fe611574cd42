# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* dynamic_accounts_report
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-28 11:11+0000\n"
"PO-Revision-Date: 2023-11-28 11:11+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "1-30"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "31-60"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "61-90"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "91-120"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-1\">\n"
"                                    Expenses\n"
"                                </span>"
msgstr "Kosten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">\n"
"                                    Income\n"
"                                </span>"
msgstr "Einkommen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">\n"
"                                    Total Income\n"
"                                </span>"
msgstr "Gesamteinkommen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">Total Expenses\n"
"                                    </span>"
msgstr "Gesamtausgaben"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-3\">\n"
"                                    Gross Profit\n"
"                                </span>"
msgstr "Bruttogewinn"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder\">\n"
"                                    Net Profit\n"
"                                </span>"
msgstr "Reingewinn"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Cost of\n"
"                                    Revenue\n"
"                                </span>"
msgstr "Umsatzkosten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Depreciation\n"
"                                    </span>"
msgstr "Abschreibung"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "<span class=\"ms-3\">Expenses</span>"
msgstr "Kosten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "<span class=\"ms-3\">Operating Income</span>"
msgstr "Betriebsergebnis"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Other Income\n"
"                                </span>"
msgstr "Anderes Einkommen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "<span style=\"font-weight: 700;\">Purchase</span>"
msgstr "Kaufen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "<span style=\"font-weight: 700;\">Sales</span>"
msgstr "Verkäufe"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        ASSETS\n"
"                                    </span>"
msgstr "VERMÖGENSWERTE"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        EQUITY\n"
"                                    </span>"
msgstr "EIGENKAPITAL"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        LIABILITIES\n"
"                                    </span>"
msgstr "VERBINDLICHKEITEN"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Current Assets\n"
"                                    </span>"
msgstr "Umlaufvermögen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Current Liabilities\n"
"                                    </span>"
msgstr "Kurzfristige Verbindlichkeiten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        LIABILITIES + EQUITY\n"
"                                    </span>"
msgstr "PASSIVA + EIGENKAPITAL"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Assets\n"
"                                    </span>"
msgstr "Gesamtvermögen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Current Assets\n"
"                                    </span>"
msgstr "Gesamten Umlaufvermögens"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Current Liabilities\n"
"                                    </span>"
msgstr "Summe kurzfristige Verbindlichkeiten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total EQUITY\n"
"                                    </span>"
msgstr "Gesamtkapital"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total LIABILITIES\n"
"                                    </span>"
msgstr "Gesamtverbindlichkeiten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Unallocated Earnings\n"
"                                    </span>"
msgstr "Gesamte nicht zugewiesene Einnahmen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Unallocated Earnings\n"
"                                    </span>"
msgstr "Nicht zugewiesene Einnahmen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Bank and Cash Accounts\n"
"                                    </span>"
msgstr "Bank- und Geldkonten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Allocated Earnings\n"
"                                    </span>"
msgstr "Aktuelle zugewiesene Einnahmen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Assets\n"
"                                    </span>"
msgstr "Aktuelle zugewiesene Einnahmen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Earnings\n"
"                                    </span>"
msgstr "Aktuelle Einnahmen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Liabilities\n"
"                                    </span>"
msgstr "Kurzfristige Verbindlichkeiten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Payables\n"
"                                    </span>"
msgstr "Verbindlichkeiten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Fixed Assets\n"
"                                    </span>"
msgstr "Plus Anlagevermögen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Non-current Assets\n"
"                                    </span>"
msgstr "Plus langfristige Vermögenswerte"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Non-current Liabilities\n"
"                                    </span>"
msgstr "Zuzüglich langfristige Verbindlichkeiten"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Prepayments\n"
"                                    </span>"
msgstr "Vorauszahlungen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Receivables\n"
"                                    </span>"
msgstr "Forderungen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Retained Earnings\n"
"                                    </span>"
msgstr "Gewinnrücklagen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
msgid ""
"<span>Unknown\n"
"                                                            Account\n"
"                                                        </span>"
msgstr "Unbekanntes Konto"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
msgid ""
"<span>Unknown\n"
"                                                            Partner\n"
"                                                        </span>"
msgstr "Unbekannter Partner"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "ASSETS"
msgstr "VERMÖGENSWERTE"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Account"
msgstr "Konto"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_bank_book_report
msgid "Account Bank Book Report"
msgstr "Kontobuchbericht"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_cash_book_report
msgid "Account Cash Book Report"
msgstr "Kontokassenbuchbericht"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__account_ids
#, python-format
msgid "Accounts"
msgstr "Konten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Accounts:"
msgstr "Konten"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_aged_payable
msgid "Age Payable"
msgstr "Alter zahlbar"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_aged_receivable
msgid "Age Receivable"
msgstr "Alter fällig"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_aged_payable
#: model:ir.ui.menu,name:dynamic_accounts_report.aged_payable_menu
msgid "Aged Payable"
msgstr "Im Alter zahlbar"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_age_payable_report
msgid "Aged Payable Report"
msgstr "Bericht über gealterte Verbindlichkeiten"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_aged_receivable
#: model:ir.ui.menu,name:dynamic_accounts_report.aged_receivable_menu
msgid "Aged Receivable"
msgstr "Veraltete Forderung"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_age_receivable_report
msgid "Aged Receivable Report"
msgstr "Bericht über gealterte Forderungen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Amount Currency"
msgstr "Betragswährung"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "Analytic Account"
msgstr ""

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__analytic_ids
msgid "Analytic Accounts"
msgstr "Analytisches Konto"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Analytic Accounts:"
msgstr "Analytische Konten:"

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__analytic_ids
msgid "Analytic accounts associated with the current record."
msgstr "Mit dem aktuellen Datensatz verknüpfte Analysekonten."

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Annotate"
msgstr "Kommentieren"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Apply"
msgstr "Anwenden"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "At Date"
msgstr "Am Datum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#, python-format
msgid "Balance"
msgstr "Gleichgewicht"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_dynamic_balance_sheet
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_balance_sheet
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_balance_sheet_report
msgid "Balance Sheet"
msgstr "Bilanz"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_bank_book
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_bank_book
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_bank_book
msgid "Bank Book"
msgstr "Bank Buch"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Bank and\n"
"                                                    Cash Accounts"
msgstr "Bank- und Geldkonten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Bank and Cash Accounts"
msgstr "Bank- und Geldkonten"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_cash_book
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_cash_book
msgid "Cash Book"
msgstr "Kassenbuch"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Cash basis method"
msgstr "Bargeldbasierte Methode"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#, python-format
msgid "Communication"
msgstr "Kommunikation"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Comparison"
msgstr "Vergleich"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Cost\n"
"                                                                of\n"
"                                                                Revenue"
msgstr "Umsatzkosten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Cost of\n"
"                                                        Revenue"
msgstr "Umsatzkosten"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Credit"
msgstr "Kredit"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Currency"
msgstr "Währung"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Allocated Earnings"
msgstr "Aktuelle zugewiesene Einnahmen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Assets"
msgstr "Umlaufvermögen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Earnings"
msgstr "Aktuelle Einnahmen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current Allocated\n"
"                                                            Earnings"
msgstr "Aktuelle zugewiesene Einnahmen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Current Assets"
msgstr "Umlaufvermögen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Current Liabilities"
msgstr "Kurzfristige Verbindlichkeiten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#, python-format
msgid "Date"
msgstr "Datum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid ""
"Date\n"
"                                            :"
msgstr "Datum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Date Range"
msgstr "Datumsbereich"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Dates"
msgstr "Termine"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Debit"
msgstr "Lastschrift"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Depreciation"
msgstr "Abschreibung"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: dynamic_accounts_report
#: model:ir.model.fields.selection,name:dynamic_accounts_report.selection__dynamic_balance_sheet_report__target_move__draft
msgid "Draft"
msgstr "Entwurf"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Due Date"
msgstr "Fälligkeitsdatum"

#. module: dynamic_accounts_report
#: model:ir.ui.menu,name:dynamic_accounts_report.dynamic_report_accounting
msgid "Dynamic Financial Reports"
msgstr "Dynamische Finanzberichte"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "EQUITY"
msgstr "EIGENKAPITAL"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"End\n"
"                                                Date\n"
"                                                :"
msgstr "Endtermin"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "End Balance"
msgstr ""

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid ""
"End Date\n"
"                                            :"
msgstr "Endtermin:"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_to
msgid "End date"
msgstr ""

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last Month"
msgstr "Ende letzten Monats"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last Quarter"
msgstr "Ende des letzten Quartals"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last year"
msgstr "Ende letzten Jahres"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Entry label"
msgstr "Eintrittsetikett"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Expected Date"
msgstr "Expected Date"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Expenses"
msgstr "Kosten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Export (XLSX)"
msgstr "Exportieren (XLSX)"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"General\n"
"                                                                                Ledger"
msgstr "Hauptbuch"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"General\n"
"                                                                    Ledger"
msgstr "Hauptbuch"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.actions.client,name:dynamic_accounts_report.action_general_ledger
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_general_ledger
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_general_ledger
#, python-format
msgid "General Ledger"
msgstr "Hauptbuch"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Hauptbuchbericht"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Generic Tax Report"
msgstr "Allgemeiner Steuerbericht"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Gross Profit"
msgstr "Bruttogewinn"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Group by: Account > Tax"
msgstr "Gruppieren nach: Konto > Steuern"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Group by: Tax > Account"
msgstr "Gruppieren nach: Steuer > Konto"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__id
msgid "ID"
msgstr "AUSWEIS"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Include Draft Entries"
msgstr "Fügen Sie Entwurfseinträge hinzu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Income"
msgstr "Einkommen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Initial Balance"
msgstr "Anfangssaldo"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Invoice Date"
msgstr "Rechnungsdatum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "JRNL"
msgstr "JRNL"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Journal"
msgstr "Tagebuch"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Journal Items"
msgstr "Journalelemente"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__journal_ids
#, python-format
msgid "Journals"
msgstr "Zeitschriften"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Journals:"
msgstr "Zeitschriften:"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "LIABILITIES"
msgstr "VERBINDLICHKEITEN"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "LIABILITIES + EQUITY"
msgstr "PASSIVA + EIGENKAPITAL"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last month"
msgstr "Im vergangenen Monat"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last quarter"
msgstr "Letztes Quartal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last year"
msgstr "Letztes Jahr"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Matching Number"
msgstr "Passende Nummer"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Move"
msgstr "Bewegen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#, python-format
msgid "NET"
msgstr "NETZ"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Net Profit"
msgstr "Reingewinn"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "No Comparison"
msgstr "Kein Vergleich"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Number of Periods:"
msgstr "Anzahl der Perioden:"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Older"
msgstr "Älter"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Open"
msgstr "Offen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Operating\n"
"                                                                Income"
msgstr "Betriebsergebnis"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Operating\n"
"                                                        Income"
msgstr "Betriebsergebnis"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Options"
msgstr "Optionen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Options : Posted Entries"
msgstr "Optionen: Gepostete Einträge"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Options : Posted Entries ,"
msgstr "Optionen: Gepostete Einträge,"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#, python-format
msgid "Options :Posted Entries"
msgstr "Optionen: Gepostete Einträge"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#, python-format
msgid "Options :Posted Entries ,"
msgstr "Optionen: Gepostete Einträge,"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Other\n"
"                                                        Income"
msgstr "Anderes Einkommen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Other Income"
msgstr "Anderes Einkommen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_partner_ledger
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_partner_ledger
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_partner_ledger
msgid "Partner Ledger"
msgstr "Partnerbuch"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_partner_ledger
msgid "Partner Ledger Report"
msgstr "Partner-Ledger-Bericht"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Payable"
msgstr "Zahlbar"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Payables"
msgstr "Verbindlichkeiten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus\n"
"                                                    Non-current Assets"
msgstr "Plus langfristige Vermögenswerte"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus\n"
"                                                    Non-current Liabilities"
msgstr "Zuzüglich langfristige Verbindlichkeiten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus Fixed\n"
"                                                    Assets"
msgstr "Plus Anlagevermögen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Fixed Assets"
msgstr "Plus Anlagevermögen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Non-current Assets"
msgstr "Plus langfristige Vermögenswerte"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Non-current Liabilities"
msgstr "Zuzüglich langfristige Verbindlichkeiten"

#. module: dynamic_accounts_report
#: model:ir.model.fields.selection,name:dynamic_accounts_report.selection__dynamic_balance_sheet_report__target_move__posted
msgid "Posted"
msgstr "Gesendet"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
msgid "Posted ,"
msgstr "Gesendet ,"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Posted Entries"
msgstr "Gepostete Einträge"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Prepayments"
msgstr "Vorauszahlungen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Previous Period"
msgstr "Vorheriger Zeitraum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Print (PDF)"
msgstr "Drucken (PDF)"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_profit_loss
msgid "Profit And Loss"
msgstr ""

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_dynamic_balance_sheet_report
msgid "Profit Loss Report"
msgstr "Gewinn-und Verlust"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_dynamic_profit_and_loss
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_profit_and_loss_report
msgid "Profit and Loss"
msgstr "Profit and Loss"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Purchase"
msgstr "Kaufen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Receivable"
msgstr "Forderungen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Receivables"
msgstr "Forderungen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Reference"
msgstr "Reference"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "Report"
msgstr "BerichtBericht"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Reports :"
msgstr "Bericht :"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Retained\n"
"                                                    Earnings"
msgstr "Gewinnrücklagen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Retained Earnings"
msgstr "Gewinnrücklagen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Sales"
msgstr "Verkäufe"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Same Period Last Year"
msgstr "Gleicher Zeitraum letztes Jahr"

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__account_ids
msgid "Select one or more accounts."
msgstr "Wählen Sie ein oder mehrere Konten aus."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__journal_ids
msgid "Select one or more journals."
msgstr "Wählen Sie eine oder mehrere Zeitschriften aus."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__company_id
msgid "Select the company to which this record belongs."
msgstr "Wählen Sie das Unternehmen aus, zu dem dieser Datensatz gehört."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__target_move
msgid "Select the target move status."
msgstr "Wählen Sie den Zielbewegungsstatus aus."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_to
msgid "Specify the end date."
msgstr "Geben Sie das Enddatum an."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_from
msgid "Specify the start date."
msgstr "Geben Sie das Startdatum an."

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Start\n"
"                                                Date :"
msgstr "Startdatum :"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid ""
"Start\n"
"                                            Date :"
msgstr "Startdatum :"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_from
msgid "Start date"
msgstr "Startdatum"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#, python-format
msgid "TAX"
msgstr "STEUER"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__target_move
msgid "Target Move"
msgstr "Zielbewegung"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Target Move:"
msgstr "Zielbewegung:"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "Target move"
msgstr "Zielbewegung"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_tax_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_tax_report
#: model:ir.model,name:dynamic_accounts_report.model_tax_report
#: model:ir.ui.menu,name:dynamic_accounts_report.tax_report_menu
msgid "Tax Report"
msgstr "Steuerbericht"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Month"
msgstr "Diesen Monat"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Quarter"
msgstr "Dieses Quartal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Year"
msgstr "Dieses Jahr"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "Today"
msgstr "Heute"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Total"
msgstr "Gesamt"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Total\n"
"                                                    Expenses"
msgstr "Gesamtausgaben"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total ASSETS"
msgstr "Gesamtvermögen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Current Assets"
msgstr "Gesamten Umlaufvermögens"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Current Liabilities"
msgstr "Summe kurzfristige Verbindlichkeiten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total EQUITY"
msgstr "Gesamtkapital"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Total Income"
msgstr "Gesamteinkommen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total LIABILITIES"
msgstr "Total LIABILITIES"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Unallocated Earnings"
msgstr "Gesamte nicht zugewiesene Einnahmen"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_trial_balance
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_trial_balance
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_trial_balance
msgid "Trial Balance"
msgstr "Probebilanz"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_trial_balance
msgid "Trial Balance Report"
msgstr "Testbilanzbericht"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Unallocated Earnings"
msgstr "Nicht zugewiesene Einnahmen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Unfold All"
msgstr "Alles entfalten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#, python-format
msgid ""
"Unknown\n"
"                                                                    Accounts"
msgstr "Unbekannte Konten"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid ""
"Unknown\n"
"                                                                    Partner"
msgstr "Unbekannter Partner"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#, python-format
msgid ""
"View\n"
"                                                                        Journal\n"
"                                                                        Entry"
msgstr "Journaleintrag anzeigen"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid ""
"View\n"
"                                                                    Journal\n"
"                                                                    Entry"
msgstr "Journaleintrag anzeigen"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
msgid "posted,"
msgstr "Gesendet"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
msgid "to"
msgstr "Zu"
