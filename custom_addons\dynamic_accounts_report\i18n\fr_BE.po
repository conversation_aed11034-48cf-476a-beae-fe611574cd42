# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* dynamic_accounts_report
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-28 11:11+0000\n"
"PO-Revision-Date: 2023-11-28 11:11+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "1-30"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "31-60"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "61-90"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
msgid "91-120"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-1\">\n"
"                                    Expenses\n"
"                                </span>"
msgstr "Dépenses"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">\n"
"                                    Income\n"
"                                </span>"
msgstr "Revenu"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">\n"
"                                    Total Income\n"
"                                </span>"
msgstr "Revenu total"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-2\">Total Expenses\n"
"                                    </span>"
msgstr "Dépenses totales"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder ms-3\">\n"
"                                    Gross Profit\n"
"                                </span>"
msgstr "Bénéfice brut"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"fw-bolder\">\n"
"                                    Net Profit\n"
"                                </span>"
msgstr "Bénéfice net"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Cost of\n"
"                                    Revenue\n"
"                                </span>"
msgstr "Coût des revenus"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Depreciation\n"
"                                    </span>"
msgstr "Dépréciation"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "<span class=\"ms-3\">Expenses</span>"
msgstr "Dépenses"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "<span class=\"ms-3\">Operating Income</span>"
msgstr "Résultat d'exploitation"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid ""
"<span class=\"ms-3\">Other Income\n"
"                                </span>"
msgstr "Autre revenu"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "<span style=\"font-weight: 700;\">Purchase</span>"
msgstr "Achat"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "<span style=\"font-weight: 700;\">Sales</span>"
msgstr "Ventes"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        ASSETS\n"
"                                    </span>"
msgstr "ACTIFS"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        EQUITY\n"
"                                    </span>"
msgstr "ÉQUITÉ"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 2%;\">\n"
"                                        LIABILITIES\n"
"                                    </span>"
msgstr "PASSIFS"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Current Assets\n"
"                                    </span>"
msgstr "Actifs actuels"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Current Liabilities\n"
"                                    </span>"
msgstr "Passif actuel"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        LIABILITIES + EQUITY\n"
"                                    </span>"
msgstr "PASSIF + CAPITAUX PROPRES"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Assets\n"
"                                    </span>"
msgstr "Actif total"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Current Assets\n"
"                                    </span>"
msgstr "Le total des actifs courants"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Current Liabilities\n"
"                                    </span>"
msgstr "Total du passif à court terme"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total EQUITY\n"
"                                    </span>"
msgstr "CAPITAUX PROPRES totaux"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total LIABILITIES\n"
"                                    </span>"
msgstr "Responsabilités totales"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Total Unallocated Earnings\n"
"                                    </span>"
msgstr "Gains totaux non répartis"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"font-weight: bolder; margin-left: 4%;\">\n"
"                                        Unallocated Earnings\n"
"                                    </span>"
msgstr "Gains non alloués"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Bank and Cash Accounts\n"
"                                    </span>"
msgstr "Comptes bancaires et espèces"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Allocated Earnings\n"
"                                    </span>"
msgstr "Gains actuellement alloués"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Assets\n"
"                                    </span>"
msgstr "Actifs actuels"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Earnings\n"
"                                    </span>"
msgstr "Gains actuels"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Current Liabilities\n"
"                                    </span>"
msgstr "Passif actuel"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Payables\n"
"                                    </span>"
msgstr "Dettes"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Fixed Assets\n"
"                                    </span>"
msgstr "Plus les immobilisations"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Non-current Assets\n"
"                                    </span>"
msgstr "Plus actifs non courants"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Plus Non-current Liabilities\n"
"                                    </span>"
msgstr "Plus passifs non courants"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Prepayments\n"
"                                    </span>"
msgstr "Paiements anticipés"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Receivables\n"
"                                    </span>"
msgstr "Créances"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
msgid ""
"<span style=\"margin-left: 6%;\">Retained Earnings\n"
"                                    </span>"
msgstr "Retained Earnings"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
msgid ""
"<span>Unknown\n"
"                                                            Account\n"
"                                                        </span>"
msgstr "Compte inconnu"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
msgid ""
"<span>Unknown\n"
"                                                            Partner\n"
"                                                        </span>"
msgstr "Partenaire inconnu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "ASSETS"
msgstr "ACTIFS"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Account"
msgstr "Compte"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_bank_book_report
msgid "Account Bank Book Report"
msgstr "Rapport du livret bancaire du compte"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_cash_book_report
msgid "Account Cash Book Report"
msgstr "Rapport du livre de caisse du compte"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__account_ids
#, python-format
msgid "Accounts"
msgstr "Comptes"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Accounts:"
msgstr "Comptes"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_aged_payable
msgid "Age Payable"
msgstr "Âge payable"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_aged_receivable
msgid "Age Receivable"
msgstr "Âge recevable"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_aged_payable
#: model:ir.ui.menu,name:dynamic_accounts_report.aged_payable_menu
msgid "Aged Payable"
msgstr "Âgé Payable"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_age_payable_report
msgid "Aged Payable Report"
msgstr "Etat des payables âgés"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_aged_receivable
#: model:ir.ui.menu,name:dynamic_accounts_report.aged_receivable_menu
msgid "Aged Receivable"
msgstr "Ancienneté à recevoir"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_age_receivable_report
msgid "Aged Receivable Report"
msgstr "Etat des créances anciennes"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Amount Currency"
msgstr "Monnaie de montant"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "Analytic Account"
msgstr "Compte analytique"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__analytic_ids
msgid "Analytic Accounts"
msgstr "Comptes analytiques"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Analytic Accounts:"
msgstr "Comptes analytiques :"

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__analytic_ids
msgid "Analytic accounts associated with the current record."
msgstr "Comptes analytiques associés à l'enregistrement actuel."

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Apply"
msgstr "Appliquer"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "At Date"
msgstr "À la date"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.balance_sheet
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#, python-format
msgid "Balance"
msgstr "Équilibre"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_dynamic_balance_sheet
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_balance_sheet
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_balance_sheet_report
msgid "Balance Sheet"
msgstr "Bilan"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_bank_book
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_bank_book
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_bank_book
msgid "Bank Book"
msgstr "Livret de banque"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Bank and\n"
"                                                    Cash Accounts"
msgstr "Comptes bancaires et espèces"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Bank and Cash Accounts"
msgstr "Comptes bancaires et espèces"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_cash_book
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_cash_book
msgid "Cash Book"
msgstr "Livre de caisse"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Cash basis method"
msgstr "Méthode de comptabilité de caisse"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#, python-format
msgid "Communication"
msgstr "Communication"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__company_id
msgid "Company"
msgstr "Entreprise"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Comparison"
msgstr "Comparaison"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Cost\n"
"                                                                of\n"
"                                                                Revenue"
msgstr "Coût des revenus"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Cost of\n"
"                                                        Revenue"
msgstr "Coût des revenus"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__create_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__create_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__create_date
msgid "Created on"
msgstr "Créé sur"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Credit"
msgstr "Crédit"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Currency"
msgstr "Devise"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Allocated Earnings"
msgstr "Gains actuellement alloués"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Assets"
msgstr "Actifs actuels"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current\n"
"                                                    Earnings"
msgstr "Gains actuels"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Current Allocated\n"
"                                                            Earnings"
msgstr "Gains actuellement alloués"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Current Assets"
msgstr "Actifs actuels"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Current Liabilities"
msgstr "Passif actuel"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#, python-format
msgid "Date"
msgstr "Date"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid ""
"Date\n"
"                                            :"
msgstr "Date"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Date Range"
msgstr "Plage de dates"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Dates"
msgstr "Rendez-vous"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Debit"
msgstr "Débit"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Depreciation"
msgstr "Dépréciation"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__display_name
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__display_name
msgid "Display Name"
msgstr "Afficher un nom"

#. module: dynamic_accounts_report
#: model:ir.model.fields.selection,name:dynamic_accounts_report.selection__dynamic_balance_sheet_report__target_move__draft
msgid "Draft"
msgstr "Brouillon"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Due Date"
msgstr "Date d'échéance"

#. module: dynamic_accounts_report
#: model:ir.ui.menu,name:dynamic_accounts_report.dynamic_report_accounting
msgid "Dynamic Financial Reports"
msgstr "Rapports financiers dynamiques"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "EQUITY"
msgstr "ÉQUITÉ"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"End\n"
"                                                Date\n"
"                                                :"
msgstr "Date de fin:"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "End Balance"
msgstr "Solde final"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid ""
"End Date\n"
"                                            :"
msgstr "Date de fin:"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_to
msgid "End date"
msgstr "Date de fin"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last Month"
msgstr "Fin du mois dernier"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last Quarter"
msgstr "Fin du dernier trimestre"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "End of Last year"
msgstr "Fin de l'année dernière"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Entry label"
msgstr "Etiquette d'entrée"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Expected Date"
msgstr "Date prévue"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Expenses"
msgstr "Dépenses"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Export (XLSX)"
msgstr "Exporter (XLSX)"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"General\n"
"                                                                                Ledger"
msgstr "Grand livre général"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"General\n"
"                                                                    Ledger"
msgstr "Grand livre général"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.actions.client,name:dynamic_accounts_report.action_general_ledger
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_general_ledger
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_general_ledger
#, python-format
msgid "General Ledger"
msgstr "Grand livre général"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Rapport du grand livre général"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Generic Tax Report"
msgstr "Rapport fiscal générique"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Gross Profit"
msgstr "Bénéfice brut"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Group by: Account > Tax"
msgstr "Regrouper par : Compte > Taxe"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Group by: Tax > Account"
msgstr "Regrouper par : Taxe > Compte"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__id
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__id
msgid "ID"
msgstr "IDENTIFIANT"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Include Draft Entries"
msgstr "Inclure les brouillons d'entrées"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Income"
msgstr "Revenu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Initial Balance"
msgstr "Balance initiale"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Invoice Date"
msgstr "Date de facture"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "JRNL"
msgstr "JRNL"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Journal"
msgstr "Journal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Journal Items"
msgstr "Éléments du journal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__journal_ids
#, python-format
msgid "Journals"
msgstr "Journaux"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Journals:"
msgstr "Journaux:"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "LIABILITIES"
msgstr "PASSIFS"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "LIABILITIES + EQUITY"
msgstr "PASSIF + CAPITAUX PROPRES"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__write_uid
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_general_ledger__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_partner_ledger__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_account_trial_balance__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_payable_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_age_receivable_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_bank_book_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_cash_book_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__write_date
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_tax_report__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last month"
msgstr "Le mois dernier"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last quarter"
msgstr "Dernier quart"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Last year"
msgstr "L'année dernière"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Matching Number"
msgstr "Numéro correspondant"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Move"
msgstr "Se déplacer"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#, python-format
msgid "NET"
msgstr "FILET"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Net Profit"
msgstr "Bénéfice net"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "No Comparison"
msgstr "Aucune comparaison"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Number of Periods:"
msgstr "Nombre de périodes :"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#, python-format
msgid "Older"
msgstr "Plus vieux"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Open"
msgstr "Ouvrir"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Operating\n"
"                                                                Income"
msgstr "en fonctionnement"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Operating\n"
"                                                        Income"
msgstr "Résultat d'exploitation"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
#, python-format
msgid "Options"
msgstr "Possibilités"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Options : Posted Entries"
msgstr "Options : Entrées publiées"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Options : Posted Entries ,"
msgstr ""

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#, python-format
msgid "Options :Posted Entries"
msgstr "Options : Entrées publiées"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#, python-format
msgid "Options :Posted Entries ,"
msgstr "Options : Entrées publiées,"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Other\n"
"                                                        Income"
msgstr "Autre revenu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Other Income"
msgstr "Autre revenu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Partner"
msgstr "Partenaire"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_partner_ledger
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_partner_ledger
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_partner_ledger
msgid "Partner Ledger"
msgstr "Grand livre des partenaires"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_partner_ledger
msgid "Partner Ledger Report"
msgstr "Rapport du grand livre partenaire"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Payable"
msgstr "Payable"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Payables"
msgstr "Dettes"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus\n"
"                                                    Non-current Assets"
msgstr "Plus actifs non courants"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus\n"
"                                                    Non-current Liabilities"
msgstr "Plus passifs non courants"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Plus Fixed\n"
"                                                    Assets"
msgstr "Plus les immobilisations"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Fixed Assets"
msgstr "Plus les immobilisations"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Non-current Assets"
msgstr "Plus actifs non courants"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Plus Non-current Liabilities"
msgstr "Plus passifs non courants"

#. module: dynamic_accounts_report
#: model:ir.model.fields.selection,name:dynamic_accounts_report.selection__dynamic_balance_sheet_report__target_move__posted
msgid "Posted"
msgstr ""

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
msgid "Posted ,"
msgstr "Publié"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Posted Entries"
msgstr "Entrées publiées"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Prepayments"
msgstr "Paiements anticipés"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Previous Period"
msgstr "Période précédente"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Print (PDF)"
msgstr "Imprimer (PDF)"

#. module: dynamic_accounts_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_profit_loss
msgid "Profit And Loss"
msgstr "Profit et perte"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_dynamic_balance_sheet_report
msgid "Profit Loss Report"
msgstr "Rapport de perte de profit"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_dynamic_profit_and_loss
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_profit_and_loss_report
msgid "Profit and Loss"
msgstr "Profit et perte"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Purchase"
msgstr "Achat"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid "Receivable"
msgstr "Recevable"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Receivables"
msgstr "Receivables"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Ref"
msgstr "Réf"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#, python-format
msgid "Reference"
msgstr "Référence"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
msgid "Report"
msgstr "Rapport"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Reports :"
msgstr "Rapport :"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid ""
"Retained\n"
"                                                    Earnings"
msgstr "Des bénéfices non répartis"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Retained Earnings"
msgstr "Des bénéfices non répartis"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#, python-format
msgid "Sales"
msgstr "Ventes"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "Same Period Last Year"
msgstr "Même période l'année dernière"

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__account_ids
msgid "Select one or more accounts."
msgstr "Sélectionnez un ou plusieurs comptes."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__journal_ids
msgid "Select one or more journals."
msgstr "Sélectionnez un ou plusieurs journaux."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__company_id
msgid "Select the company to which this record belongs."
msgstr "Sélectionnez la société à laquelle appartient cet enregistrement."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__target_move
msgid "Select the target move status."
msgstr "Sélectionnez le statut du déplacement cible."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_to
msgid "Specify the end date."
msgstr "Précisez la date de fin."

#. module: dynamic_accounts_report
#: model:ir.model.fields,help:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_from
msgid "Specify the start date."
msgstr "Précisez la date de fin."

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Start\n"
"                                                Date :"
msgstr "Date de début :"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid ""
"Start\n"
"                                            Date :"
msgstr "Date de début :"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__date_from
msgid "Start date"
msgstr "Date de début"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#, python-format
msgid "TAX"
msgstr "IMPÔT"

#. module: dynamic_accounts_report
#: model:ir.model.fields,field_description:dynamic_accounts_report.field_dynamic_balance_sheet_report__target_move
msgid "Target Move"
msgstr "Déplacement cible"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Target Move:"
msgstr "Déplacement cible :"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.profit_loss
msgid "Target move"
msgstr "Mouvement cible"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_tax_report
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_tax_report
#: model:ir.model,name:dynamic_accounts_report.model_tax_report
#: model:ir.ui.menu,name:dynamic_accounts_report.tax_report_menu
msgid "Tax Report"
msgstr "Rapport fiscal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Month"
msgstr "Ce mois-ci"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Quarter"
msgstr "Ce trimestre"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/tax_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#, python-format
msgid "This Year"
msgstr "Cette année"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#, python-format
msgid "Today"
msgstr "Aujourd'hui"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/trial_balance_view.xml:0
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_payable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.aged_receivable
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#, python-format
msgid "Total"
msgstr "Totale"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid ""
"Total\n"
"                                                    Expenses"
msgstr "Dépenses totales"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total ASSETS"
msgstr "ACTIF total"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Current Assets"
msgstr "Le total des actifs courants"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Current Liabilities"
msgstr "Total du passif à court terme"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total EQUITY"
msgstr "CAPITAUX PROPRES totaux"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Total Income"
msgstr "Revenu total"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total LIABILITIES"
msgstr "Responsabilités totales"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Total Unallocated Earnings"
msgstr "Gains totaux non répartis"

#. module: dynamic_accounts_report
#: model:ir.actions.client,name:dynamic_accounts_report.action_trial_balance
#: model:ir.actions.report,name:dynamic_accounts_report.action_print_trial_balance
#: model:ir.ui.menu,name:dynamic_accounts_report.menu_trial_balance
msgid "Trial Balance"
msgstr "Balance de vérification"

#. module: dynamic_accounts_report
#: model:ir.model,name:dynamic_accounts_report.model_account_trial_balance
msgid "Trial Balance Report"
msgstr "Rapport de balance de vérification"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#, python-format
msgid "Unallocated Earnings"
msgstr "Gains non alloués"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/balance_sheet_template.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/profit_and_loss_templates.xml:0
#, python-format
msgid "Unfold All"
msgstr "Déplier tout"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#, python-format
msgid ""
"Unknown\n"
"                                                                    Accounts"
msgstr "Comptes inconnus"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid ""
"Unknown\n"
"                                                                    Partner"
msgstr "Partenaire inconnu"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_payable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/aged_receivable_report_views.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/bank_flow_templates.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/cash_flow_templates.xml:0
#, python-format
msgid ""
"View\n"
"                                                                        Journal\n"
"                                                                        Entry"
msgstr "Afficher l'écriture de journal"

#. module: dynamic_accounts_report
#. odoo-javascript
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/cust1/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/general_ledger_view.xml:0
#: code:addons/dynamic_accounts_report/static/src/xml/partner_ledger_view.xml:0
#, python-format
msgid ""
"View\n"
"                                                                    Journal\n"
"                                                                    Entry"
msgstr "Afficher l'écriture de journal"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
msgid "posted,"
msgstr "posté"

#. module: dynamic_accounts_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.bank_book
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.general_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.partner_ledger
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.tax_report
#: model_terms:ir.ui.view,arch_db:dynamic_accounts_report.trial_balance
msgid "to"
msgstr "à"
