<?xml version="1.0" encoding="UTF-8"?>
<odoo>
<!--Account Aged Trial Balance Form View-->
    <record id="account_aged_trial_balance_view_form" model="ir.ui.view">
        <field name="name">account.aged.trial.balance.view.form</field>
        <field name="model">account.aged.trial.balance</field>
        <field name="arch" type="xml">
            <form string="Report Options">
                <group col="4">
                    <field name="date_from"/>
                    <field name="period_length"/>
                    <newline/>
                    <field name="result_selection" widget="radio"/>
                    <field name="target_move" widget="radio"/>
                </group>
                <footer>
                    <button name="check_report" string="Print" type="object" default_focus="1" class="oe_highlight"/>
                    <button string="Cancel" class="btn btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
<!--Action Account Aged Trial Balance-->
    <record id="action_account_aged_balance_view" model="ir.actions.act_window">
        <field name="name">Aged Partner Balance</field>
        <field name="res_model">account.aged.trial.balance</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="account_aged_trial_balance_view_form"/>
        <field name="context">{}</field>
        <field name="target">new</field>
    </record>
<!--Menu Aged Partner Balance-->
    <menuitem id="menu_aged_trial_balance"
              name="Aged Partner Balance"
              sequence="2"
              action="action_account_aged_balance_view"
              parent="account.menu_finance_reports"/>
</odoo>
