<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
<!--  FollowUp View Form  -->
    <record id="account_followup_view_form" model="ir.ui.view">
        <field name="name">account.followup.view.form</field>
        <field name="model">account.followup</field>
        <field name="arch" type="xml">
            <form string="Follow-up">
                <h1><field name="name"/></h1>
                <label for="company_id" groups="base.group_multi_company"/>
                <field name="company_id" options="{'no_create': True}" class="oe_inline"
                       groups="base.group_multi_company"/>
                <p class="oe_grey">
                    To remind customers of paying their invoices, you can
                    define different actions depending on how severely
                    overdue the customer is. These actions are bundled
                    into follow-up levels that are triggered when the due
                    date of an invoice has passed a certain
                    number of days. If there are other overdue invoices for the
                    same customer, the actions of the most
                    overdue invoice will be executed.
                </p>
                <field name="followup_line_ids"/>
            </form>
        </field>
    </record>
<!--  FollowUp View Tree  -->
    <record id="account_followup_view_tree" model="ir.ui.view">
        <field name="name">account.followup.view.tree</field>
        <field name="model">account.followup</field>
        <field name="arch" type="xml">
            <list string="Follow-up">
                <field name="company_id" />
            </list>
        </field>
    </record>
<!--  FollowUp Search View  -->
    <record id="account_followup_view_search" model="ir.ui.view">
        <field name="name">account.followup.view.search</field>
        <field name="model">account.followup</field>
        <field name="arch" type="xml">
            <search string="Search Follow-up">
                <field name="company_id" groups="base.group_multi_company"/>
            </search>
        </field>
    </record>
<!--  FollowUp Kanban View  -->
    <record id="account_followup_view_kanban" model="ir.ui.view">
        <field name="name">account.followup.view.kanban</field>
        <field name="model">account.followup</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div>
                                <strong><i class="fa fa-building" role="img" aria-label="Enterprise"/> <t t-esc="record.name.value"/></strong>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
<!--  Action FollowUp  -->
    <record id="action_account_followup_definition_form" model="ir.actions.act_window">
        <field name="name">Payment Follow-ups</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.followup</field>
        <field name="search_view_id" ref="account_followup_view_search"/>
        <field name="view_mode">list,kanban,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Define follow-up levels and their related actions
            </p>
            <p>
                For each step, specify the actions to be taken and delay in days. It is
                possible to use print and e-mail templates to send specific messages to
                the customer.
            </p>
        </field>
    </record>
<!--  FollowUp MenuItem  -->
    <menuitem action="action_account_followup_definition_form" id="account_followup_menu"
              parent="account.account_management_menu" name="Follow-up Levels"
              groups="account.group_account_manager" sequence="2"/>
</odoo>
