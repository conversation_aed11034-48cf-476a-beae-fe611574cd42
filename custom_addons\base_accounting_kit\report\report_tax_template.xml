<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="report_tax">
        <t t-call="web.html_container">
            <t t-set="data_report_margin_top" t-value="12"/>
            <t t-set="data_report_header_spacing" t-value="9"/>
            <t t-set="data_report_dpi" t-value="110"/>
            <t t-call="web.internal_layout">
                <div class="page"><br/>
                    <h3>Tax Report</h3>
                    <div class="row">
                        <div class="col-3">
                            <strong>Company:</strong>
                            <p t-esc="env.company.name"/>
                        </div>
                        <div>
                            <t t-if="data['date_from']">
                                <strong>Date from :</strong>
                                <span t-esc="data['date_from']"/>
                            </t>
                            <br/>
                            <t t-if="data['date_to']">
                                <strong>Date to :</strong>
                                <span t-esc="data['date_to']"/>
                            </t>
                        </div>
                    </div>
                    <table class="table table-sm table-reports">
                        <thead>
                            <tr class="text-centre">
                                <th>Sale</th>
                                <th>Net</th>
                                <th>Tax</th>
                            </tr>
                        </thead>
                        <tr align="left" t-foreach="lines['sale']" t-as="line">
                            <td>
                                <span t-esc="line.get('name')"/>
                            </td>
                            <td>
                                <span t-att-style="style" t-esc="line.get('net')"
                                      t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                            </td>
                            <td>
                                <span t-att-style="style" t-esc="line.get('tax')"
                                      t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                            </td>
                        </tr>
                        <br/>
                        <tr align="left">
                            <td>
                                <strong>Purchase</strong>
                            </td>
                            <td/>
                            <td/>
                        </tr>
                        <tr align="left" t-foreach="lines['purchase']" t-as="line">
                            <td>
                                <span t-esc="line.get('name')"/>
                            </td>
                            <td>
                                <span t-att-style="style" t-esc="line.get('net')"
                                      t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                            </td>
                            <td>
                                <span t-att-style="style" t-esc="line.get('tax')"
                                      t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                            </td>
                        </tr>
                    </table>
                </div>
            </t>
        </t>
    </template>
</odoo>
